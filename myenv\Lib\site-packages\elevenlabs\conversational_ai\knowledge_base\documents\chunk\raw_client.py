# This file was auto-generated by <PERSON>rn from our API Definition.

import typing
from json.decoder import JSONDecodeError

from .....core.api_error import ApiError
from .....core.client_wrapper import Async<PERSON>lient<PERSON>rapper, SyncClientWrapper
from .....core.http_response import AsyncHttpResponse, HttpResponse
from .....core.jsonable_encoder import jsonable_encoder
from .....core.request_options import RequestOptions
from .....core.unchecked_base_model import construct_type
from .....errors.unprocessable_entity_error import UnprocessableEntityError
from .....types.http_validation_error import HttpValidationError
from .....types.knowledge_base_document_chunk_response_model import KnowledgeBaseDocumentChunkResponseModel


class RawChunkClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._client_wrapper = client_wrapper

    def get(
        self, documentation_id: str, chunk_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> HttpResponse[KnowledgeBaseDocumentChunkResponseModel]:
        """
        Get details about a specific documentation part used by RAG.

        Parameters
        ----------
        documentation_id : str
            The id of a document from the knowledge base. This is returned on document addition.

        chunk_id : str
            The id of a document RAG chunk from the knowledge base.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        HttpResponse[KnowledgeBaseDocumentChunkResponseModel]
            Successful Response
        """
        _response = self._client_wrapper.httpx_client.request(
            f"v1/convai/knowledge-base/{jsonable_encoder(documentation_id)}/chunk/{jsonable_encoder(chunk_id)}",
            method="GET",
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                _data = typing.cast(
                    KnowledgeBaseDocumentChunkResponseModel,
                    construct_type(
                        type_=KnowledgeBaseDocumentChunkResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
                return HttpResponse(response=_response, data=_data)
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    headers=dict(_response.headers),
                    body=typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response.text)
        raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)


class AsyncRawChunkClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._client_wrapper = client_wrapper

    async def get(
        self, documentation_id: str, chunk_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> AsyncHttpResponse[KnowledgeBaseDocumentChunkResponseModel]:
        """
        Get details about a specific documentation part used by RAG.

        Parameters
        ----------
        documentation_id : str
            The id of a document from the knowledge base. This is returned on document addition.

        chunk_id : str
            The id of a document RAG chunk from the knowledge base.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AsyncHttpResponse[KnowledgeBaseDocumentChunkResponseModel]
            Successful Response
        """
        _response = await self._client_wrapper.httpx_client.request(
            f"v1/convai/knowledge-base/{jsonable_encoder(documentation_id)}/chunk/{jsonable_encoder(chunk_id)}",
            method="GET",
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                _data = typing.cast(
                    KnowledgeBaseDocumentChunkResponseModel,
                    construct_type(
                        type_=KnowledgeBaseDocumentChunkResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
                return AsyncHttpResponse(response=_response, data=_data)
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    headers=dict(_response.headers),
                    body=typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response.text)
        raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)

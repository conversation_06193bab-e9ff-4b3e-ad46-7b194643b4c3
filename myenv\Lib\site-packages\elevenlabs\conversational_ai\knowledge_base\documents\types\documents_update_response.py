# This file was auto-generated by <PERSON><PERSON> from our API Definition.

from __future__ import annotations

import typing

import pydantic
import typing_extensions
from .....core.pydantic_utilities import IS_PYDANTIC_V2
from .....core.unchecked_base_model import UncheckedBaseModel, UnionMetadata
from .....types.document_usage_mode_enum import DocumentUsageModeEnum
from .....types.knowledge_base_document_metadata_response_model import KnowledgeBaseDocumentMetadataResponseModel
from .....types.resource_access_info import ResourceAccessInfo


class DocumentsUpdateResponse_Url(UncheckedBaseModel):
    type: typing.Literal["url"] = "url"
    id: str
    name: str
    metadata: KnowledgeBaseDocumentMetadataResponseModel
    supported_usages: typing.List[DocumentUsageModeEnum]
    access_info: ResourceAccessInfo
    extracted_inner_html: str
    url: str

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow


class DocumentsUpdateResponse_File(UncheckedBaseModel):
    type: typing.Literal["file"] = "file"
    id: str
    name: str
    metadata: KnowledgeBaseDocumentMetadataResponseModel
    supported_usages: typing.List[DocumentUsageModeEnum]
    access_info: ResourceAccessInfo
    extracted_inner_html: str

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow


class DocumentsUpdateResponse_Text(UncheckedBaseModel):
    type: typing.Literal["text"] = "text"
    id: str
    name: str
    metadata: KnowledgeBaseDocumentMetadataResponseModel
    supported_usages: typing.List[DocumentUsageModeEnum]
    access_info: ResourceAccessInfo
    extracted_inner_html: str

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow


DocumentsUpdateResponse = typing_extensions.Annotated[
    typing.Union[DocumentsUpdateResponse_Url, DocumentsUpdateResponse_File, DocumentsUpdateResponse_Text],
    UnionMetadata(discriminant="type"),
]

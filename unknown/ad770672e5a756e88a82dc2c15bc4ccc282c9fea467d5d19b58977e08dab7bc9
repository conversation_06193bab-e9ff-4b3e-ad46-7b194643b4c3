{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/cloud-platform": {"description": "See, edit, configure, and delete your Google Cloud data and see the email address for your Google Account."}, "https://www.googleapis.com/auth/trace.append": {"description": "Write Trace data for a project or application"}, "https://www.googleapis.com/auth/trace.readonly": {"description": "Read Trace data for a project or application"}}}}, "basePath": "", "baseUrl": "https://cloudtrace.googleapis.com/", "batchPath": "batch", "canonicalName": "Cloud Trace", "description": "Sends application trace data to Cloud Trace for viewing. Trace data is collected for all App Engine applications by default. Trace data from other applications can be provided using this API. This library is used to interact with the Cloud Trace API directly. If you are looking to instrument your application for Cloud Trace, we recommend using OpenTelemetry. ", "discoveryVersion": "v1", "documentationLink": "https://cloud.google.com/trace", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "cloudtrace:v1", "kind": "discovery#restDescription", "mtlsRootUrl": "https://cloudtrace.mtls.googleapis.com/", "name": "cloudtrace", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"projects": {"methods": {"patchTraces": {"description": "Sends trace spans to Cloud Trace. Spans cannot be updated. If the trace ID and span ID already exist, an additional copy of the span will be stored.", "flatPath": "v1/projects/{projectId}/traces", "httpMethod": "PATCH", "id": "cloudtrace.projects.patchTraces", "parameterOrder": ["projectId"], "parameters": {"projectId": {"description": "Required. ID of the Cloud project where the trace data is stored.", "location": "path", "required": true, "type": "string"}}, "path": "v1/projects/{projectId}/traces", "request": {"$ref": "Traces"}, "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/trace.append"]}}, "resources": {"traces": {"methods": {"get": {"description": "Gets a single trace by its ID.", "flatPath": "v1/projects/{projectId}/traces/{traceId}", "httpMethod": "GET", "id": "cloudtrace.projects.traces.get", "parameterOrder": ["projectId", "traceId"], "parameters": {"projectId": {"description": "Required. ID of the Cloud project where the trace data is stored.", "location": "path", "required": true, "type": "string"}, "traceId": {"description": "Required. ID of the trace to return.", "location": "path", "required": true, "type": "string"}}, "path": "v1/projects/{projectId}/traces/{traceId}", "response": {"$ref": "Trace"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/trace.readonly"]}, "list": {"description": "Returns a list of traces that match the specified filter conditions.", "flatPath": "v1/projects/{projectId}/traces", "httpMethod": "GET", "id": "cloudtrace.projects.traces.list", "parameterOrder": ["projectId"], "parameters": {"endTime": {"description": "End of the time interval (inclusive) during which the trace data was collected from the application.", "format": "google-datetime", "location": "query", "type": "string"}, "filter": {"description": "Optional. A filter against properties of the trace. See [filter syntax documentation](https://cloud.google.com/trace/docs/trace-filters) for details.", "location": "query", "type": "string"}, "orderBy": {"description": "Optional. Field used to sort the returned traces. Can be one of the following: * `trace_id` * `name` (`name` field of root span in the trace) * `duration` (difference between `end_time` and `start_time` fields of the root span) * `start` (`start_time` field of the root span) Descending order can be specified by appending `desc` to the sort field (for example, `name desc`). Only one sort field is permitted.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. Maximum number of traces to return. If not specified or <= 0, the implementation selects a reasonable value. The implementation may return fewer traces than the requested page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Token identifying the page of results to return. If provided, use the value of the `next_page_token` field from a previous request.", "location": "query", "type": "string"}, "projectId": {"description": "Required. ID of the Cloud project where the trace data is stored.", "location": "path", "required": true, "type": "string"}, "startTime": {"description": "Start of the time interval (inclusive) during which the trace data was collected from the application.", "format": "google-datetime", "location": "query", "type": "string"}, "view": {"description": "Optional. Type of data returned for traces in the list. Default is `MINIMAL`.", "enum": ["VIEW_TYPE_UNSPECIFIED", "MINIMAL", "ROOTSPAN", "COMPLETE"], "enumDescriptions": ["De<PERSON>ult is `MINIMAL` if unspecified.", "Minimal view of the trace record that contains only the project and trace IDs.", "Root span view of the trace record that returns the root spans along with the minimal trace data.", "Complete view of the trace record that contains the actual trace data. This is equivalent to calling the REST `get` or RPC `GetTrace` method using the ID of each listed trace."], "location": "query", "type": "string"}}, "path": "v1/projects/{projectId}/traces", "response": {"$ref": "ListTracesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/trace.readonly"]}}}}}}, "revision": "20250530", "rootUrl": "https://cloudtrace.googleapis.com/", "schemas": {"Empty": {"description": "A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }", "id": "Empty", "properties": {}, "type": "object"}, "ListTracesResponse": {"description": "The response message for the `ListTraces` method.", "id": "ListTracesResponse", "properties": {"nextPageToken": {"description": "If defined, indicates that there are more traces that match the request and that this value should be passed to the next request to continue retrieving additional traces.", "type": "string"}, "traces": {"description": "List of trace records as specified by the view parameter.", "items": {"$ref": "Trace"}, "type": "array"}}, "type": "object"}, "Trace": {"description": "A trace describes how long it takes for an application to perform an operation. It consists of a set of spans, each of which represent a single timed event within the operation.", "id": "Trace", "properties": {"projectId": {"description": "Project ID of the Cloud project where the trace data is stored.", "type": "string"}, "spans": {"description": "Collection of spans in the trace.", "items": {"$ref": "TraceSpan"}, "type": "array"}, "traceId": {"description": "Globally unique identifier for the trace. This identifier is a 128-bit numeric value formatted as a 32-byte hex string. For example, `382d4f4c6b7bb2f4a972559d9085001d`. The numeric value should not be zero.", "type": "string"}}, "type": "object"}, "TraceSpan": {"description": "A span represents a single timed event within a trace. Spans can be nested and form a trace tree. Often, a trace contains a root span that describes the end-to-end latency of an operation and, optionally, one or more subspans for its suboperations. Spans do not need to be contiguous. There may be gaps between spans in a trace.", "id": "TraceSpan", "properties": {"endTime": {"description": "End time of the span in seconds and nanoseconds from the UNIX epoch.", "format": "google-datetime", "type": "string"}, "kind": {"description": "Distinguishes between spans generated in a particular context. For example, two spans with the same name may be distinguished using `RPC_CLIENT` and `RPC_SERVER` to identify queueing latency associated with the span.", "enum": ["SPAN_KIND_UNSPECIFIED", "RPC_SERVER", "RPC_CLIENT"], "enumDescriptions": ["Unspecified.", "Indicates that the span covers server-side handling of an RPC or other remote network request.", "Indicates that the span covers the client-side wrapper around an RPC or other remote request."], "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Collection of labels associated with the span. Label keys must be less than 128 bytes. Label values must be less than 16 KiB. Some keys might have predefined meaning, and you can also create your own. For more information, see [Cloud Trace labels](https://cloud.google.com/trace/docs/trace-labels).", "type": "object"}, "name": {"description": "Name of the span. Must be less than 128 bytes. The span name is sanitized and displayed in the Trace tool in the Google Cloud Platform Console. The name may be a method name or some other per-call site name. For the same executable and the same call point, a best practice is to use a consistent name, which makes it easier to correlate cross-trace spans.", "type": "string"}, "parentSpanId": {"description": "Optional. ID of the parent span, if any.", "format": "uint64", "type": "string"}, "spanId": {"description": "Identifier for the span. Must be a 64-bit integer other than 0 and unique within a trace. For example, `2205310701640571284`.", "format": "uint64", "type": "string"}, "startTime": {"description": "Start time of the span in seconds and nanoseconds from the UNIX epoch.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "Traces": {"description": "List of new or updated traces.", "id": "Traces", "properties": {"traces": {"description": "List of traces.", "items": {"$ref": "Trace"}, "type": "array"}}, "type": "object"}}, "servicePath": "", "title": "Cloud Trace API", "version": "v1", "version_module": true}
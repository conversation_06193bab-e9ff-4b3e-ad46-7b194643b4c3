# This file was auto-generated by <PERSON><PERSON> from our API Definition.

from __future__ import annotations

import typing

import pydantic
import typing_extensions
from ....core.pydantic_utilities import IS_PYDANTIC_V2
from ....core.unchecked_base_model import UncheckedBaseModel, UnionMetadata
from ....types.inbound_sip_trunk_config_request_model import InboundSipTrunkConfigRequestModel
from ....types.outbound_sip_trunk_config_request_model import OutboundSipTrunkConfigRequestModel


class PhoneNumbersCreateRequestBody_Twilio(UncheckedBaseModel):
    """
    Create Phone Request Information
    """

    provider: typing.Literal["twilio"] = "twilio"
    phone_number: str
    label: str
    supports_inbound: typing.Optional[bool] = None
    supports_outbound: typing.Optional[bool] = None
    sid: str
    token: str

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow


class PhoneNumbersCreateRequestBody_SipTrunk(UncheckedBaseModel):
    """
    Create Phone Request Information
    """

    provider: typing.Literal["sip_trunk"] = "sip_trunk"
    phone_number: str
    label: str
    supports_inbound: typing.Optional[bool] = None
    supports_outbound: typing.Optional[bool] = None
    inbound_trunk_config: typing.Optional[InboundSipTrunkConfigRequestModel] = None
    outbound_trunk_config: typing.Optional[OutboundSipTrunkConfigRequestModel] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow


PhoneNumbersCreateRequestBody = typing_extensions.Annotated[
    typing.Union[PhoneNumbersCreateRequestBody_Twilio, PhoneNumbersCreateRequestBody_SipTrunk],
    UnionMetadata(discriminant="provider"),
]

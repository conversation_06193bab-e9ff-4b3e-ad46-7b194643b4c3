#!/usr/bin/env python3
"""
Test script for the optimized auto_video_generator.py
This script tests individual components without making actual API calls.
"""

import os
import sys
import tempfile
from pathlib import Path
from unittest.mock import Mock, patch

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_config():
    """Test configuration management"""
    print("🧪 Testing configuration management...")
    
    # Set test environment variables
    os.environ.update({
        'GEMINI_API_KEY': 'test_gemini_key',
        'ELEVENLABS_API_KEY': 'test_elevenlabs_key',
        'DID_API_KEY': 'test_did_key',
        'IG_USERNAME': 'test_username',
        'IG_PASSWORD': 'test_password'
    })
    
    try:
        from auto_video_generator import Config
        config = Config()
        
        assert config.gemini_api_key == 'test_gemini_key'
        assert config.elevenlabs_api_key == 'test_elevenlabs_key'
        assert config.did_api_key == 'test_did_key'
        assert config.ig_username == 'test_username'
        assert config.ig_password == 'test_password'
        
        # Test defaults
        assert config.voice_model == 'eleven_monolingual_v1'
        assert config.voice_name == 'Rachel'
        assert config.video_fps == 24
        
        print("✅ Configuration test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False

def test_http_client():
    """Test HTTP client with retry logic"""
    print("🧪 Testing HTTP client...")
    
    try:
        from auto_video_generator import HTTPClientManager
        
        client = HTTPClientManager(max_retries=2, timeout=10)
        
        # Test that client is properly configured
        assert client.timeout == 10
        assert client.session is not None
        
        client.close()
        print("✅ HTTP client test passed!")
        return True
        
    except Exception as e:
        print(f"❌ HTTP client test failed: {e}")
        return False

def test_temporary_directory():
    """Test temporary directory context manager"""
    print("🧪 Testing temporary directory management...")
    
    try:
        from auto_video_generator import temporary_directory
        
        temp_path = None
        with temporary_directory() as temp_dir:
            temp_path = temp_dir
            assert temp_path.exists()
            assert temp_path.is_dir()
            
            # Create a test file
            test_file = temp_path / "test.txt"
            test_file.write_text("test content")
            assert test_file.exists()
        
        # Directory should be cleaned up after context
        assert not temp_path.exists()
        
        print("✅ Temporary directory test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Temporary directory test failed: {e}")
        return False

def test_video_generator_init():
    """Test VideoGenerator initialization"""
    print("🧪 Testing VideoGenerator initialization...")
    
    try:
        from auto_video_generator import VideoGenerator, Config
        
        config = Config()
        
        # Mock the API setup to avoid actual API calls
        with patch('auto_video_generator.genai.configure'), \
             patch('auto_video_generator.ElevenLabs'):

            generator = VideoGenerator(config)
            
            assert generator.config == config
            assert generator.http_client is not None
            
            generator.cleanup()
            
        print("✅ VideoGenerator initialization test passed!")
        return True
        
    except Exception as e:
        print(f"❌ VideoGenerator initialization test failed: {e}")
        return False

def test_script_generation_mock():
    """Test script generation with mocked API"""
    print("🧪 Testing script generation (mocked)...")
    
    try:
        from auto_video_generator import VideoGenerator, Config
        
        config = Config()
        
        # Mock the entire Gemini API
        mock_response = Mock()
        mock_response.text = "This is a test script for Instagram Reel."
        
        mock_model = Mock()
        mock_model.generate_content.return_value = mock_response
        
        with patch('auto_video_generator.genai.configure'), \
             patch('auto_video_generator.ElevenLabs'), \
             patch('auto_video_generator.genai.GenerativeModel', return_value=mock_model):

            generator = VideoGenerator(config)
            script = generator.generate_script()
            
            assert script == "This is a test script for Instagram Reel."
            assert mock_model.generate_content.called
            
            generator.cleanup()
            
        print("✅ Script generation test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Script generation test failed: {e}")
        return False

def run_all_tests():
    """Run all tests"""
    print("🚀 Running optimization tests...\n")
    
    tests = [
        test_config,
        test_http_client,
        test_temporary_directory,
        test_video_generator_init,
        test_script_generation_mock
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()  # Add spacing between tests
    
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The optimization is working correctly.")
        return True
    else:
        print("⚠️ Some tests failed. Please check the implementation.")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)

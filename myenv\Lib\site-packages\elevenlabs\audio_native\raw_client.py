# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing
from json.decoder import <PERSON><PERSON><PERSON>ecodeError

from .. import core
from ..core.api_error import ApiError
from ..core.client_wrapper import Async<PERSON>lient<PERSON>rapper, SyncClientWrapper
from ..core.http_response import AsyncHttpResponse, HttpResponse
from ..core.jsonable_encoder import jsonable_encoder
from ..core.request_options import RequestOptions
from ..core.unchecked_base_model import construct_type
from ..errors.unprocessable_entity_error import UnprocessableEntityError
from ..types.audio_native_create_project_response_model import AudioNativeCreateProjectResponseModel
from ..types.audio_native_edit_content_response_model import AudioNativeEditContentResponseModel
from ..types.get_audio_native_project_settings_response_model import GetAudioNativeProjectSettingsResponseModel
from ..types.http_validation_error import HttpValidationError

# this is used as the default value for optional parameters
OMIT = typing.cast(typing.Any, ...)


class RawAudioNativeClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._client_wrapper = client_wrapper

    def create(
        self,
        *,
        name: str,
        image: typing.Optional[str] = OMIT,
        author: typing.Optional[str] = OMIT,
        title: typing.Optional[str] = OMIT,
        small: typing.Optional[bool] = OMIT,
        text_color: typing.Optional[str] = OMIT,
        background_color: typing.Optional[str] = OMIT,
        sessionization: typing.Optional[int] = OMIT,
        voice_id: typing.Optional[str] = OMIT,
        model_id: typing.Optional[str] = OMIT,
        file: typing.Optional[core.File] = OMIT,
        auto_convert: typing.Optional[bool] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> HttpResponse[AudioNativeCreateProjectResponseModel]:
        """
        Creates Audio Native enabled project, optionally starts conversion and returns project ID and embeddable HTML snippet.

        Parameters
        ----------
        name : str
            Project name.

        image : typing.Optional[str]
            (Deprecated) Image URL used in the player. If not provided, default image set in the Player settings is used.

        author : typing.Optional[str]
            Author used in the player and inserted at the start of the uploaded article. If not provided, the default author set in the Player settings is used.

        title : typing.Optional[str]
            Title used in the player and inserted at the top of the uploaded article. If not provided, the default title set in the Player settings is used.

        small : typing.Optional[bool]
            (Deprecated) Whether to use small player or not. If not provided, default value set in the Player settings is used.

        text_color : typing.Optional[str]
            Text color used in the player. If not provided, default text color set in the Player settings is used.

        background_color : typing.Optional[str]
            Background color used in the player. If not provided, default background color set in the Player settings is used.

        sessionization : typing.Optional[int]
            (Deprecated) Specifies for how many minutes to persist the session across page reloads. If not provided, default sessionization set in the Player settings is used.

        voice_id : typing.Optional[str]
            Voice ID used to voice the content. If not provided, default voice ID set in the Player settings is used.

        model_id : typing.Optional[str]
            TTS Model ID used in the player. If not provided, default model ID set in the Player settings is used.

        file : typing.Optional[core.File]
            See core.File for more documentation

        auto_convert : typing.Optional[bool]
            Whether to auto convert the project to audio or not.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        HttpResponse[AudioNativeCreateProjectResponseModel]
            Successful Response
        """
        _response = self._client_wrapper.httpx_client.request(
            "v1/audio-native",
            method="POST",
            data={
                "name": name,
                "image": image,
                "author": author,
                "title": title,
                "small": small,
                "text_color": text_color,
                "background_color": background_color,
                "sessionization": sessionization,
                "voice_id": voice_id,
                "model_id": model_id,
                "auto_convert": auto_convert,
            },
            files={
                **({"file": file} if file is not None else {}),
            },
            request_options=request_options,
            omit=OMIT,
            force_multipart=True,
        )
        try:
            if 200 <= _response.status_code < 300:
                _data = typing.cast(
                    AudioNativeCreateProjectResponseModel,
                    construct_type(
                        type_=AudioNativeCreateProjectResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
                return HttpResponse(response=_response, data=_data)
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    headers=dict(_response.headers),
                    body=typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response.text)
        raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)

    def get_settings(
        self, project_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> HttpResponse[GetAudioNativeProjectSettingsResponseModel]:
        """
        Get player settings for the specific project.

        Parameters
        ----------
        project_id : str
            The ID of the Studio project.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        HttpResponse[GetAudioNativeProjectSettingsResponseModel]
            Successful Response
        """
        _response = self._client_wrapper.httpx_client.request(
            f"v1/audio-native/{jsonable_encoder(project_id)}/settings",
            method="GET",
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                _data = typing.cast(
                    GetAudioNativeProjectSettingsResponseModel,
                    construct_type(
                        type_=GetAudioNativeProjectSettingsResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
                return HttpResponse(response=_response, data=_data)
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    headers=dict(_response.headers),
                    body=typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response.text)
        raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)

    def update(
        self,
        project_id: str,
        *,
        file: typing.Optional[core.File] = OMIT,
        auto_convert: typing.Optional[bool] = OMIT,
        auto_publish: typing.Optional[bool] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> HttpResponse[AudioNativeEditContentResponseModel]:
        """
        Updates content for the specific AudioNative Project.

        Parameters
        ----------
        project_id : str
            The ID of the project to be used. You can use the [List projects](/docs/api-reference/studio/get-projects) endpoint to list all the available projects.

        file : typing.Optional[core.File]
            See core.File for more documentation

        auto_convert : typing.Optional[bool]
            Whether to auto convert the project to audio or not.

        auto_publish : typing.Optional[bool]
            Whether to auto publish the new project snapshot after it's converted.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        HttpResponse[AudioNativeEditContentResponseModel]
            Successful Response
        """
        _response = self._client_wrapper.httpx_client.request(
            f"v1/audio-native/{jsonable_encoder(project_id)}/content",
            method="POST",
            data={
                "auto_convert": auto_convert,
                "auto_publish": auto_publish,
            },
            files={
                **({"file": file} if file is not None else {}),
            },
            request_options=request_options,
            omit=OMIT,
            force_multipart=True,
        )
        try:
            if 200 <= _response.status_code < 300:
                _data = typing.cast(
                    AudioNativeEditContentResponseModel,
                    construct_type(
                        type_=AudioNativeEditContentResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
                return HttpResponse(response=_response, data=_data)
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    headers=dict(_response.headers),
                    body=typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response.text)
        raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)


class AsyncRawAudioNativeClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._client_wrapper = client_wrapper

    async def create(
        self,
        *,
        name: str,
        image: typing.Optional[str] = OMIT,
        author: typing.Optional[str] = OMIT,
        title: typing.Optional[str] = OMIT,
        small: typing.Optional[bool] = OMIT,
        text_color: typing.Optional[str] = OMIT,
        background_color: typing.Optional[str] = OMIT,
        sessionization: typing.Optional[int] = OMIT,
        voice_id: typing.Optional[str] = OMIT,
        model_id: typing.Optional[str] = OMIT,
        file: typing.Optional[core.File] = OMIT,
        auto_convert: typing.Optional[bool] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> AsyncHttpResponse[AudioNativeCreateProjectResponseModel]:
        """
        Creates Audio Native enabled project, optionally starts conversion and returns project ID and embeddable HTML snippet.

        Parameters
        ----------
        name : str
            Project name.

        image : typing.Optional[str]
            (Deprecated) Image URL used in the player. If not provided, default image set in the Player settings is used.

        author : typing.Optional[str]
            Author used in the player and inserted at the start of the uploaded article. If not provided, the default author set in the Player settings is used.

        title : typing.Optional[str]
            Title used in the player and inserted at the top of the uploaded article. If not provided, the default title set in the Player settings is used.

        small : typing.Optional[bool]
            (Deprecated) Whether to use small player or not. If not provided, default value set in the Player settings is used.

        text_color : typing.Optional[str]
            Text color used in the player. If not provided, default text color set in the Player settings is used.

        background_color : typing.Optional[str]
            Background color used in the player. If not provided, default background color set in the Player settings is used.

        sessionization : typing.Optional[int]
            (Deprecated) Specifies for how many minutes to persist the session across page reloads. If not provided, default sessionization set in the Player settings is used.

        voice_id : typing.Optional[str]
            Voice ID used to voice the content. If not provided, default voice ID set in the Player settings is used.

        model_id : typing.Optional[str]
            TTS Model ID used in the player. If not provided, default model ID set in the Player settings is used.

        file : typing.Optional[core.File]
            See core.File for more documentation

        auto_convert : typing.Optional[bool]
            Whether to auto convert the project to audio or not.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AsyncHttpResponse[AudioNativeCreateProjectResponseModel]
            Successful Response
        """
        _response = await self._client_wrapper.httpx_client.request(
            "v1/audio-native",
            method="POST",
            data={
                "name": name,
                "image": image,
                "author": author,
                "title": title,
                "small": small,
                "text_color": text_color,
                "background_color": background_color,
                "sessionization": sessionization,
                "voice_id": voice_id,
                "model_id": model_id,
                "auto_convert": auto_convert,
            },
            files={
                **({"file": file} if file is not None else {}),
            },
            request_options=request_options,
            omit=OMIT,
            force_multipart=True,
        )
        try:
            if 200 <= _response.status_code < 300:
                _data = typing.cast(
                    AudioNativeCreateProjectResponseModel,
                    construct_type(
                        type_=AudioNativeCreateProjectResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
                return AsyncHttpResponse(response=_response, data=_data)
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    headers=dict(_response.headers),
                    body=typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response.text)
        raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)

    async def get_settings(
        self, project_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> AsyncHttpResponse[GetAudioNativeProjectSettingsResponseModel]:
        """
        Get player settings for the specific project.

        Parameters
        ----------
        project_id : str
            The ID of the Studio project.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AsyncHttpResponse[GetAudioNativeProjectSettingsResponseModel]
            Successful Response
        """
        _response = await self._client_wrapper.httpx_client.request(
            f"v1/audio-native/{jsonable_encoder(project_id)}/settings",
            method="GET",
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                _data = typing.cast(
                    GetAudioNativeProjectSettingsResponseModel,
                    construct_type(
                        type_=GetAudioNativeProjectSettingsResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
                return AsyncHttpResponse(response=_response, data=_data)
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    headers=dict(_response.headers),
                    body=typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response.text)
        raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)

    async def update(
        self,
        project_id: str,
        *,
        file: typing.Optional[core.File] = OMIT,
        auto_convert: typing.Optional[bool] = OMIT,
        auto_publish: typing.Optional[bool] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> AsyncHttpResponse[AudioNativeEditContentResponseModel]:
        """
        Updates content for the specific AudioNative Project.

        Parameters
        ----------
        project_id : str
            The ID of the project to be used. You can use the [List projects](/docs/api-reference/studio/get-projects) endpoint to list all the available projects.

        file : typing.Optional[core.File]
            See core.File for more documentation

        auto_convert : typing.Optional[bool]
            Whether to auto convert the project to audio or not.

        auto_publish : typing.Optional[bool]
            Whether to auto publish the new project snapshot after it's converted.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AsyncHttpResponse[AudioNativeEditContentResponseModel]
            Successful Response
        """
        _response = await self._client_wrapper.httpx_client.request(
            f"v1/audio-native/{jsonable_encoder(project_id)}/content",
            method="POST",
            data={
                "auto_convert": auto_convert,
                "auto_publish": auto_publish,
            },
            files={
                **({"file": file} if file is not None else {}),
            },
            request_options=request_options,
            omit=OMIT,
            force_multipart=True,
        )
        try:
            if 200 <= _response.status_code < 300:
                _data = typing.cast(
                    AudioNativeEditContentResponseModel,
                    construct_type(
                        type_=AudioNativeEditContentResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
                return AsyncHttpResponse(response=_response, data=_data)
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    headers=dict(_response.headers),
                    body=typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response.text)
        raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)

# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

from ..core.client_wrapper import Async<PERSON><PERSON><PERSON>rapper, SyncClientWrapper
from ..core.request_options import RequestOptions
from ..types.delete_sample_response import DeleteSampleResponse
from .raw_client import Async<PERSON>aw<PERSON>amplesClient, RawSamplesClient


class SamplesClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._raw_client = RawSamplesClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> RawSamplesClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        RawSamplesClient
        """
        return self._raw_client

    def delete(
        self, voice_id: str, sample_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> DeleteSampleResponse:
        """
        Removes a sample by its ID.

        Parameters
        ----------
        voice_id : str
            ID of the voice to be used. You can use the [Get voices](/docs/api-reference/voices/search) endpoint list all the available voices.

        sample_id : str
            ID of the sample to be used. You can use the [Get voices](/docs/api-reference/voices/get) endpoint list all the available samples for a voice.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        DeleteSampleResponse
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.samples.delete(
            voice_id="21m00Tcm4TlvDq8ikWAM",
            sample_id="VW7YKqPnjY4h39yTbx2L",
        )
        """
        _response = self._raw_client.delete(voice_id, sample_id, request_options=request_options)
        return _response.data


class AsyncSamplesClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._raw_client = AsyncRawSamplesClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> AsyncRawSamplesClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        AsyncRawSamplesClient
        """
        return self._raw_client

    async def delete(
        self, voice_id: str, sample_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> DeleteSampleResponse:
        """
        Removes a sample by its ID.

        Parameters
        ----------
        voice_id : str
            ID of the voice to be used. You can use the [Get voices](/docs/api-reference/voices/search) endpoint list all the available voices.

        sample_id : str
            ID of the sample to be used. You can use the [Get voices](/docs/api-reference/voices/get) endpoint list all the available samples for a voice.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        DeleteSampleResponse
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.samples.delete(
                voice_id="21m00Tcm4TlvDq8ikWAM",
                sample_id="VW7YKqPnjY4h39yTbx2L",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.delete(voice_id, sample_id, request_options=request_options)
        return _response.data

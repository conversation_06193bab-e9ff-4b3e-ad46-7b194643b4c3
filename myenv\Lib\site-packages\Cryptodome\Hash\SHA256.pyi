from typing import Union, Optional


class SHA256Hash(object):
    digest_size: int
    block_size: int
    oid: str
    def __init__(self, data: Optional[Union[bytes, bytearray, memoryview]]=None) -> None: ...
    def update(self, data: Union[bytes, bytearray, memoryview]) -> None: ...
    def digest(self) -> bytes: ...
    def hexdigest(self) -> str: ...
    def copy(self) -> SHA256Hash: ...
    def new(self, data: Optional[Union[bytes, bytearray, memoryview]]=None) -> SHA256Hash: ...

def new(data: Optional[Union[bytes, bytearray, memoryview]]=None) -> SHA256Hash: ...

digest_size: int
block_size: int

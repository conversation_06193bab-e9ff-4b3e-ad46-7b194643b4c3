#!/usr/bin/env python3
"""
Credit-efficient testing script for D-ID trial account
Optimized for 12 credits (3 videos max)
"""

from auto_video_generator import test_with_credits, VideoGenerator, Config
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def estimate_credits(duration_seconds: int) -> float:
    """Estimate D-ID credits needed for video duration"""
    return (duration_seconds / 30) * 4

def test_plan_optimizer():
    """Show optimized testing plan for 12 credits"""
    print("🎯 D-ID Credit Optimization Plan")
    print("=" * 50)
    print(f"💰 Available Credits: 12")
    print(f"📊 Cost per 30s: 4 credits")
    print(f"🎬 Max Videos: 3")
    print()
    
    test_scenarios = [
        {"name": "Quick Hook Test", "duration": 15, "topic": "morning routine"},
        {"name": "Standard Reel", "duration": 25, "topic": "productivity tips"}, 
        {"name": "Full Test", "duration": 30, "topic": "success mindset"}
    ]
    
    total_credits = 0
    for i, scenario in enumerate(test_scenarios, 1):
        credits = estimate_credits(scenario["duration"])
        total_credits += credits
        print(f"Test {i}: {scenario['name']}")
        print(f"  📝 Topic: {scenario['topic']}")
        print(f"  ⏱️ Duration: {scenario['duration']}s")
        print(f"  💰 Credits: {credits:.1f}")
        print()
    
    print(f"📊 Total Credits Needed: {total_credits:.1f}/12")
    print(f"🎯 Credits Remaining: {12 - total_credits:.1f}")
    print()

def run_credit_efficient_test(test_number: int = 1):
    """Run a specific credit-efficient test"""
    
    test_configs = [
        {"topic": "morning routine", "duration": 15},  # ~2 credits
        {"topic": "productivity tips", "duration": 25},  # ~3.3 credits  
        {"topic": "success mindset", "duration": 30}   # 4 credits
    ]
    
    if test_number < 1 or test_number > len(test_configs):
        print(f"❌ Invalid test number. Choose 1-{len(test_configs)}")
        return
    
    config = test_configs[test_number - 1]
    credits_needed = estimate_credits(config["duration"])
    
    print(f"🚀 Running Test {test_number}")
    print(f"📝 Topic: {config['topic']}")
    print(f"⏱️ Duration: {config['duration']}s")
    print(f"💰 Credits: {credits_needed:.1f}")
    print("-" * 30)
    
    try:
        results = test_with_credits(
            topic=config["topic"],
            duration=config["duration"]
        )
        
        if results["success"]:
            print("✅ Test completed successfully!")
            print(f"📝 Script length: {len(results['script'])} characters")
            print(f"🎤 Voice file: {results['voice_file']}")
            print(f"🎬 Video file: {results.get('instagram_video', 'N/A')}")
        else:
            print("❌ Test failed:")
            for error in results["errors"]:
                print(f"   {error}")
                
    except Exception as e:
        print(f"❌ Test error: {e}")

def check_script_only(topic: str = "motivation", duration: int = 20):
    """Test script generation only (no credits used)"""
    print(f"📝 Testing Script Generation Only (No Credits Used)")
    print(f"Topic: {topic}, Duration: {duration}s")
    print("-" * 40)
    
    try:
        generator = VideoGenerator(Config())
        script = generator.generate_script(
            topic=topic,
            tone="energetic", 
            duration=duration
        )
        
        word_count = len(script.split())
        estimated_time = (word_count / 150) * 60
        
        print("✅ Script Generated:")
        print(f"📊 Length: {len(script)} characters")
        print(f"📊 Words: {word_count}")
        print(f"📊 Estimated time: {estimated_time:.1f}s")
        print(f"📊 Target time: {duration}s")
        print()
        print("📝 Script Preview:")
        print("-" * 20)
        print(script[:200] + "..." if len(script) > 200 else script)
        
        generator.cleanup()
        
    except Exception as e:
        print(f"❌ Script generation failed: {e}")

def main():
    """Main testing interface"""
    print("🎬 D-ID Credit-Efficient Testing Tool")
    print("=" * 50)
    
    while True:
        print("\nChoose an option:")
        print("1. Show optimization plan")
        print("2. Test script generation only (FREE)")
        print("3. Run Test 1: Quick Hook (15s, ~2 credits)")
        print("4. Run Test 2: Standard Reel (25s, ~3.3 credits)")
        print("5. Run Test 3: Full Test (30s, 4 credits)")
        print("6. Exit")
        
        choice = input("\nEnter choice (1-6): ").strip()
        
        if choice == "1":
            test_plan_optimizer()
        elif choice == "2":
            topic = input("Enter topic (or press Enter for 'motivation'): ").strip() or "motivation"
            check_script_only(topic=topic)
        elif choice in ["3", "4", "5"]:
            test_num = int(choice) - 2
            run_credit_efficient_test(test_num)
        elif choice == "6":
            print("👋 Happy testing!")
            break
        else:
            print("❌ Invalid choice. Please try again.")

if __name__ == "__main__":
    main()

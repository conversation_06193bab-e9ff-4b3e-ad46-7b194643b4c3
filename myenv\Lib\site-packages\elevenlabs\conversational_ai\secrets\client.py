# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

from ...core.client_wrapper import Async<PERSON><PERSON><PERSON>rapper, SyncClientWrapper
from ...core.request_options import RequestOptions
from ...types.get_workspace_secrets_response_model import GetWorkspaceSecretsResponseModel
from ...types.post_workspace_secret_response_model import PostWorkspaceSecretResponseModel
from .raw_client import AsyncRawSecretsClient, RawSecretsClient

# this is used as the default value for optional parameters
OMIT = typing.cast(typing.Any, ...)


class SecretsClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._raw_client = RawSecretsClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> RawSecretsClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        RawSecretsClient
        """
        return self._raw_client

    def list(self, *, request_options: typing.Optional[RequestOptions] = None) -> GetWorkspaceSecretsResponseModel:
        """
        Get all workspace secrets for the user

        Parameters
        ----------
        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        GetWorkspaceSecretsResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.conversational_ai.secrets.list()
        """
        _response = self._raw_client.list(request_options=request_options)
        return _response.data

    def create(
        self, *, name: str, value: str, request_options: typing.Optional[RequestOptions] = None
    ) -> PostWorkspaceSecretResponseModel:
        """
        Create a new secret for the workspace

        Parameters
        ----------
        name : str

        value : str

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        PostWorkspaceSecretResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.conversational_ai.secrets.create(
            name="name",
            value="value",
        )
        """
        _response = self._raw_client.create(name=name, value=value, request_options=request_options)
        return _response.data

    def delete(self, secret_id: str, *, request_options: typing.Optional[RequestOptions] = None) -> None:
        """
        Delete a workspace secret if it's not in use

        Parameters
        ----------
        secret_id : str

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        None

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.conversational_ai.secrets.delete(
            secret_id="secret_id",
        )
        """
        _response = self._raw_client.delete(secret_id, request_options=request_options)
        return _response.data


class AsyncSecretsClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._raw_client = AsyncRawSecretsClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> AsyncRawSecretsClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        AsyncRawSecretsClient
        """
        return self._raw_client

    async def list(
        self, *, request_options: typing.Optional[RequestOptions] = None
    ) -> GetWorkspaceSecretsResponseModel:
        """
        Get all workspace secrets for the user

        Parameters
        ----------
        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        GetWorkspaceSecretsResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.conversational_ai.secrets.list()


        asyncio.run(main())
        """
        _response = await self._raw_client.list(request_options=request_options)
        return _response.data

    async def create(
        self, *, name: str, value: str, request_options: typing.Optional[RequestOptions] = None
    ) -> PostWorkspaceSecretResponseModel:
        """
        Create a new secret for the workspace

        Parameters
        ----------
        name : str

        value : str

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        PostWorkspaceSecretResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.conversational_ai.secrets.create(
                name="name",
                value="value",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.create(name=name, value=value, request_options=request_options)
        return _response.data

    async def delete(self, secret_id: str, *, request_options: typing.Optional[RequestOptions] = None) -> None:
        """
        Delete a workspace secret if it's not in use

        Parameters
        ----------
        secret_id : str

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        None

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.conversational_ai.secrets.delete(
                secret_id="secret_id",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.delete(secret_id, request_options=request_options)
        return _response.data

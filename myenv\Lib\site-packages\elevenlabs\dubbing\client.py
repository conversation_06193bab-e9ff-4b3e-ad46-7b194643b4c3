# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

from .. import core
from ..core.client_wrapper import AsyncC<PERSON><PERSON>rapper, SyncClientWrapper
from ..core.request_options import RequestOptions
from ..types.delete_dubbing_response_model import DeleteDubbingResponseModel
from ..types.do_dubbing_response import DoDubbingResponse
from ..types.dubbing_metadata_page_response_model import DubbingMetadataPageResponseModel
from ..types.dubbing_metadata_response import DubbingMetadataResponse
from .audio.client import AsyncAudioClient, AudioClient
from .raw_client import AsyncRawDubbingClient, RawDubbingClient
from .resource.client import AsyncResourceClient, ResourceClient
from .transcript.client import AsyncTranscriptClient, TranscriptClient
from .types.dubbing_list_request_dubbing_status import DubbingListRequestDubbingStatus
from .types.dubbing_list_request_filter_by_creator import DubbingListRequestFilterByCreator

# this is used as the default value for optional parameters
OMIT = typing.cast(typing.Any, ...)


class DubbingClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._raw_client = RawDubbingClient(client_wrapper=client_wrapper)
        self.resource = ResourceClient(client_wrapper=client_wrapper)

        self.audio = AudioClient(client_wrapper=client_wrapper)

        self.transcript = TranscriptClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> RawDubbingClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        RawDubbingClient
        """
        return self._raw_client

    def list(
        self,
        *,
        cursor: typing.Optional[str] = None,
        page_size: typing.Optional[int] = None,
        dubbing_status: typing.Optional[DubbingListRequestDubbingStatus] = None,
        filter_by_creator: typing.Optional[DubbingListRequestFilterByCreator] = None,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> DubbingMetadataPageResponseModel:
        """
        List the dubs you have access to.

        Parameters
        ----------
        cursor : typing.Optional[str]
            Used for fetching next page. Cursor is returned in the response.

        page_size : typing.Optional[int]
            How many dubs to return at maximum. Can not exceed 200, defaults to 100.

        dubbing_status : typing.Optional[DubbingListRequestDubbingStatus]
            Status of the dubbing, either 'in_progress', 'completed' or 'failed'.

        filter_by_creator : typing.Optional[DubbingListRequestFilterByCreator]
            Filters who created the resources being listed, whether it was the user running the request or someone else that shared the resource with them.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        DubbingMetadataPageResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.dubbing.list()
        """
        _response = self._raw_client.list(
            cursor=cursor,
            page_size=page_size,
            dubbing_status=dubbing_status,
            filter_by_creator=filter_by_creator,
            request_options=request_options,
        )
        return _response.data

    def create(
        self,
        *,
        file: typing.Optional[core.File] = OMIT,
        csv_file: typing.Optional[core.File] = OMIT,
        foreground_audio_file: typing.Optional[core.File] = OMIT,
        background_audio_file: typing.Optional[core.File] = OMIT,
        name: typing.Optional[str] = OMIT,
        source_url: typing.Optional[str] = OMIT,
        source_lang: typing.Optional[str] = OMIT,
        target_lang: typing.Optional[str] = OMIT,
        num_speakers: typing.Optional[int] = OMIT,
        watermark: typing.Optional[bool] = OMIT,
        start_time: typing.Optional[int] = OMIT,
        end_time: typing.Optional[int] = OMIT,
        highest_resolution: typing.Optional[bool] = OMIT,
        drop_background_audio: typing.Optional[bool] = OMIT,
        use_profanity_filter: typing.Optional[bool] = OMIT,
        dubbing_studio: typing.Optional[bool] = OMIT,
        disable_voice_cloning: typing.Optional[bool] = OMIT,
        mode: typing.Optional[str] = OMIT,
        csv_fps: typing.Optional[float] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> DoDubbingResponse:
        """
        Dubs a provided audio or video file into given language.

        Parameters
        ----------
        file : typing.Optional[core.File]
            See core.File for more documentation

        csv_file : typing.Optional[core.File]
            See core.File for more documentation

        foreground_audio_file : typing.Optional[core.File]
            See core.File for more documentation

        background_audio_file : typing.Optional[core.File]
            See core.File for more documentation

        name : typing.Optional[str]
            Name of the dubbing project.

        source_url : typing.Optional[str]
            URL of the source video/audio file.

        source_lang : typing.Optional[str]
            Source language.

        target_lang : typing.Optional[str]
            The Target language to dub the content into.

        num_speakers : typing.Optional[int]
            Number of speakers to use for the dubbing. Set to 0 to automatically detect the number of speakers

        watermark : typing.Optional[bool]
            Whether to apply watermark to the output video.

        start_time : typing.Optional[int]
            Start time of the source video/audio file.

        end_time : typing.Optional[int]
            End time of the source video/audio file.

        highest_resolution : typing.Optional[bool]
            Whether to use the highest resolution available.

        drop_background_audio : typing.Optional[bool]
            An advanced setting. Whether to drop background audio from the final dub. This can improve dub quality where it's known that audio shouldn't have a background track such as for speeches or monologues.

        use_profanity_filter : typing.Optional[bool]
            [BETA] Whether transcripts should have profanities censored with the words '[censored]'

        dubbing_studio : typing.Optional[bool]
            Whether to prepare dub for edits in dubbing studio or edits as a dubbing resource.

        disable_voice_cloning : typing.Optional[bool]
            [BETA] Instead of using a voice clone in dubbing, use a similar voice from the ElevenLabs Voice Library.

        mode : typing.Optional[str]
            automatic or manual. Manual mode is only supported when creating a dubbing studio project

        csv_fps : typing.Optional[float]
            Frames per second to use when parsing a CSV file for dubbing. If not provided, FPS will be inferred from timecodes.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        DoDubbingResponse
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.dubbing.create()
        """
        _response = self._raw_client.create(
            file=file,
            csv_file=csv_file,
            foreground_audio_file=foreground_audio_file,
            background_audio_file=background_audio_file,
            name=name,
            source_url=source_url,
            source_lang=source_lang,
            target_lang=target_lang,
            num_speakers=num_speakers,
            watermark=watermark,
            start_time=start_time,
            end_time=end_time,
            highest_resolution=highest_resolution,
            drop_background_audio=drop_background_audio,
            use_profanity_filter=use_profanity_filter,
            dubbing_studio=dubbing_studio,
            disable_voice_cloning=disable_voice_cloning,
            mode=mode,
            csv_fps=csv_fps,
            request_options=request_options,
        )
        return _response.data

    def get(
        self, dubbing_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> DubbingMetadataResponse:
        """
        Returns metadata about a dubbing project, including whether it's still in progress or not

        Parameters
        ----------
        dubbing_id : str
            ID of the dubbing project.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        DubbingMetadataResponse
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.dubbing.get(
            dubbing_id="dubbing_id",
        )
        """
        _response = self._raw_client.get(dubbing_id, request_options=request_options)
        return _response.data

    def delete(
        self, dubbing_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> DeleteDubbingResponseModel:
        """
        Deletes a dubbing project.

        Parameters
        ----------
        dubbing_id : str
            ID of the dubbing project.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        DeleteDubbingResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.dubbing.delete(
            dubbing_id="dubbing_id",
        )
        """
        _response = self._raw_client.delete(dubbing_id, request_options=request_options)
        return _response.data


class AsyncDubbingClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._raw_client = AsyncRawDubbingClient(client_wrapper=client_wrapper)
        self.resource = AsyncResourceClient(client_wrapper=client_wrapper)

        self.audio = AsyncAudioClient(client_wrapper=client_wrapper)

        self.transcript = AsyncTranscriptClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> AsyncRawDubbingClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        AsyncRawDubbingClient
        """
        return self._raw_client

    async def list(
        self,
        *,
        cursor: typing.Optional[str] = None,
        page_size: typing.Optional[int] = None,
        dubbing_status: typing.Optional[DubbingListRequestDubbingStatus] = None,
        filter_by_creator: typing.Optional[DubbingListRequestFilterByCreator] = None,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> DubbingMetadataPageResponseModel:
        """
        List the dubs you have access to.

        Parameters
        ----------
        cursor : typing.Optional[str]
            Used for fetching next page. Cursor is returned in the response.

        page_size : typing.Optional[int]
            How many dubs to return at maximum. Can not exceed 200, defaults to 100.

        dubbing_status : typing.Optional[DubbingListRequestDubbingStatus]
            Status of the dubbing, either 'in_progress', 'completed' or 'failed'.

        filter_by_creator : typing.Optional[DubbingListRequestFilterByCreator]
            Filters who created the resources being listed, whether it was the user running the request or someone else that shared the resource with them.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        DubbingMetadataPageResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.dubbing.list()


        asyncio.run(main())
        """
        _response = await self._raw_client.list(
            cursor=cursor,
            page_size=page_size,
            dubbing_status=dubbing_status,
            filter_by_creator=filter_by_creator,
            request_options=request_options,
        )
        return _response.data

    async def create(
        self,
        *,
        file: typing.Optional[core.File] = OMIT,
        csv_file: typing.Optional[core.File] = OMIT,
        foreground_audio_file: typing.Optional[core.File] = OMIT,
        background_audio_file: typing.Optional[core.File] = OMIT,
        name: typing.Optional[str] = OMIT,
        source_url: typing.Optional[str] = OMIT,
        source_lang: typing.Optional[str] = OMIT,
        target_lang: typing.Optional[str] = OMIT,
        num_speakers: typing.Optional[int] = OMIT,
        watermark: typing.Optional[bool] = OMIT,
        start_time: typing.Optional[int] = OMIT,
        end_time: typing.Optional[int] = OMIT,
        highest_resolution: typing.Optional[bool] = OMIT,
        drop_background_audio: typing.Optional[bool] = OMIT,
        use_profanity_filter: typing.Optional[bool] = OMIT,
        dubbing_studio: typing.Optional[bool] = OMIT,
        disable_voice_cloning: typing.Optional[bool] = OMIT,
        mode: typing.Optional[str] = OMIT,
        csv_fps: typing.Optional[float] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> DoDubbingResponse:
        """
        Dubs a provided audio or video file into given language.

        Parameters
        ----------
        file : typing.Optional[core.File]
            See core.File for more documentation

        csv_file : typing.Optional[core.File]
            See core.File for more documentation

        foreground_audio_file : typing.Optional[core.File]
            See core.File for more documentation

        background_audio_file : typing.Optional[core.File]
            See core.File for more documentation

        name : typing.Optional[str]
            Name of the dubbing project.

        source_url : typing.Optional[str]
            URL of the source video/audio file.

        source_lang : typing.Optional[str]
            Source language.

        target_lang : typing.Optional[str]
            The Target language to dub the content into.

        num_speakers : typing.Optional[int]
            Number of speakers to use for the dubbing. Set to 0 to automatically detect the number of speakers

        watermark : typing.Optional[bool]
            Whether to apply watermark to the output video.

        start_time : typing.Optional[int]
            Start time of the source video/audio file.

        end_time : typing.Optional[int]
            End time of the source video/audio file.

        highest_resolution : typing.Optional[bool]
            Whether to use the highest resolution available.

        drop_background_audio : typing.Optional[bool]
            An advanced setting. Whether to drop background audio from the final dub. This can improve dub quality where it's known that audio shouldn't have a background track such as for speeches or monologues.

        use_profanity_filter : typing.Optional[bool]
            [BETA] Whether transcripts should have profanities censored with the words '[censored]'

        dubbing_studio : typing.Optional[bool]
            Whether to prepare dub for edits in dubbing studio or edits as a dubbing resource.

        disable_voice_cloning : typing.Optional[bool]
            [BETA] Instead of using a voice clone in dubbing, use a similar voice from the ElevenLabs Voice Library.

        mode : typing.Optional[str]
            automatic or manual. Manual mode is only supported when creating a dubbing studio project

        csv_fps : typing.Optional[float]
            Frames per second to use when parsing a CSV file for dubbing. If not provided, FPS will be inferred from timecodes.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        DoDubbingResponse
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.dubbing.create()


        asyncio.run(main())
        """
        _response = await self._raw_client.create(
            file=file,
            csv_file=csv_file,
            foreground_audio_file=foreground_audio_file,
            background_audio_file=background_audio_file,
            name=name,
            source_url=source_url,
            source_lang=source_lang,
            target_lang=target_lang,
            num_speakers=num_speakers,
            watermark=watermark,
            start_time=start_time,
            end_time=end_time,
            highest_resolution=highest_resolution,
            drop_background_audio=drop_background_audio,
            use_profanity_filter=use_profanity_filter,
            dubbing_studio=dubbing_studio,
            disable_voice_cloning=disable_voice_cloning,
            mode=mode,
            csv_fps=csv_fps,
            request_options=request_options,
        )
        return _response.data

    async def get(
        self, dubbing_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> DubbingMetadataResponse:
        """
        Returns metadata about a dubbing project, including whether it's still in progress or not

        Parameters
        ----------
        dubbing_id : str
            ID of the dubbing project.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        DubbingMetadataResponse
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.dubbing.get(
                dubbing_id="dubbing_id",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.get(dubbing_id, request_options=request_options)
        return _response.data

    async def delete(
        self, dubbing_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> DeleteDubbingResponseModel:
        """
        Deletes a dubbing project.

        Parameters
        ----------
        dubbing_id : str
            ID of the dubbing project.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        DeleteDubbingResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.dubbing.delete(
                dubbing_id="dubbing_id",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.delete(dubbing_id, request_options=request_options)
        return _response.data

# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

from ....core.client_wrapper import AsyncC<PERSON><PERSON>rapper, SyncClientWrapper
from ....core.request_options import RequestOptions
from ....types.get_agent_link_response_model import GetAgentLinkResponseModel
from .raw_client import AsyncRaw<PERSON>inkClient, RawLinkClient


class LinkClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._raw_client = RawLinkClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> RawLinkClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        RawLinkClient
        """
        return self._raw_client

    def get(
        self, agent_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> GetAgentLinkResponseModel:
        """
        Get the current link used to share the agent with others

        Parameters
        ----------
        agent_id : str
            The id of an agent. This is returned on agent creation.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        GetAgentLinkResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.conversational_ai.agents.link.get(
            agent_id="21m00Tcm4TlvDq8ikWAM",
        )
        """
        _response = self._raw_client.get(agent_id, request_options=request_options)
        return _response.data


class AsyncLinkClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._raw_client = AsyncRawLinkClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> AsyncRawLinkClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        AsyncRawLinkClient
        """
        return self._raw_client

    async def get(
        self, agent_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> GetAgentLinkResponseModel:
        """
        Get the current link used to share the agent with others

        Parameters
        ----------
        agent_id : str
            The id of an agent. This is returned on agent creation.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        GetAgentLinkResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.conversational_ai.agents.link.get(
                agent_id="21m00Tcm4TlvDq8ikWAM",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.get(agent_id, request_options=request_options)
        return _response.data

# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

from .....core.client_wrapper import AsyncClient<PERSON>rapper, SyncClientWrapper
from .....core.request_options import RequestOptions
from .....types.chapter_snapshot_extended_response_model import ChapterSnapshotExtendedResponseModel
from .....types.chapter_snapshots_response import ChapterSnapshotsResponse
from .raw_client import AsyncRawSnapshotsClient, RawSnapshotsClient

# this is used as the default value for optional parameters
OMIT = typing.cast(typing.Any, ...)


class SnapshotsClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._raw_client = RawSnapshotsClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> RawSnapshotsClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        RawSnapshotsClient
        """
        return self._raw_client

    def list(
        self, project_id: str, chapter_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> ChapterSnapshotsResponse:
        """
        Gets information about all the snapshots of a chapter. Each snapshot can be downloaded as audio. Whenever a chapter is converted a snapshot will automatically be created.

        Parameters
        ----------
        project_id : str
            The ID of the project to be used. You can use the [List projects](/docs/api-reference/studio/get-projects) endpoint to list all the available projects.

        chapter_id : str
            The ID of the chapter to be used. You can use the [List project chapters](/docs/api-reference/studio/get-chapters) endpoint to list all the available chapters.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        ChapterSnapshotsResponse
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.studio.projects.chapters.snapshots.list(
            project_id="21m00Tcm4TlvDq8ikWAM",
            chapter_id="21m00Tcm4TlvDq8ikWAM",
        )
        """
        _response = self._raw_client.list(project_id, chapter_id, request_options=request_options)
        return _response.data

    def get(
        self,
        project_id: str,
        chapter_id: str,
        chapter_snapshot_id: str,
        *,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> ChapterSnapshotExtendedResponseModel:
        """
        Returns the chapter snapshot.

        Parameters
        ----------
        project_id : str
            The ID of the Studio project.

        chapter_id : str
            The ID of the chapter.

        chapter_snapshot_id : str
            The ID of the chapter snapshot.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        ChapterSnapshotExtendedResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.studio.projects.chapters.snapshots.get(
            project_id="21m00Tcm4TlvDq8ikWAM",
            chapter_id="21m00Tcm4TlvDq8ikWAM",
            chapter_snapshot_id="21m00Tcm4TlvDq8ikWAM",
        )
        """
        _response = self._raw_client.get(project_id, chapter_id, chapter_snapshot_id, request_options=request_options)
        return _response.data

    def stream(
        self,
        project_id: str,
        chapter_id: str,
        chapter_snapshot_id: str,
        *,
        convert_to_mpeg: typing.Optional[bool] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> typing.Iterator[bytes]:
        """
        Stream the audio from a chapter snapshot. Use `GET /v1/studio/projects/{project_id}/chapters/{chapter_id}/snapshots` to return the snapshots of a chapter.

        Parameters
        ----------
        project_id : str
            The ID of the project to be used. You can use the [List projects](/docs/api-reference/studio/get-projects) endpoint to list all the available projects.

        chapter_id : str
            The ID of the chapter to be used. You can use the [List project chapters](/docs/api-reference/studio/get-chapters) endpoint to list all the available chapters.

        chapter_snapshot_id : str
            The ID of the chapter snapshot to be used. You can use the [List project chapter snapshots](/docs/api-reference/studio/get-snapshots) endpoint to list all the available snapshots.

        convert_to_mpeg : typing.Optional[bool]
            Whether to convert the audio to mpeg format.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration. You can pass in configuration such as `chunk_size`, and more to customize the request and response.

        Returns
        -------
        typing.Iterator[bytes]
            Streaming audio data
        """
        with self._raw_client.stream(
            project_id,
            chapter_id,
            chapter_snapshot_id,
            convert_to_mpeg=convert_to_mpeg,
            request_options=request_options,
        ) as r:
            yield from r.data


class AsyncSnapshotsClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._raw_client = AsyncRawSnapshotsClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> AsyncRawSnapshotsClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        AsyncRawSnapshotsClient
        """
        return self._raw_client

    async def list(
        self, project_id: str, chapter_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> ChapterSnapshotsResponse:
        """
        Gets information about all the snapshots of a chapter. Each snapshot can be downloaded as audio. Whenever a chapter is converted a snapshot will automatically be created.

        Parameters
        ----------
        project_id : str
            The ID of the project to be used. You can use the [List projects](/docs/api-reference/studio/get-projects) endpoint to list all the available projects.

        chapter_id : str
            The ID of the chapter to be used. You can use the [List project chapters](/docs/api-reference/studio/get-chapters) endpoint to list all the available chapters.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        ChapterSnapshotsResponse
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.studio.projects.chapters.snapshots.list(
                project_id="21m00Tcm4TlvDq8ikWAM",
                chapter_id="21m00Tcm4TlvDq8ikWAM",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.list(project_id, chapter_id, request_options=request_options)
        return _response.data

    async def get(
        self,
        project_id: str,
        chapter_id: str,
        chapter_snapshot_id: str,
        *,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> ChapterSnapshotExtendedResponseModel:
        """
        Returns the chapter snapshot.

        Parameters
        ----------
        project_id : str
            The ID of the Studio project.

        chapter_id : str
            The ID of the chapter.

        chapter_snapshot_id : str
            The ID of the chapter snapshot.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        ChapterSnapshotExtendedResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.studio.projects.chapters.snapshots.get(
                project_id="21m00Tcm4TlvDq8ikWAM",
                chapter_id="21m00Tcm4TlvDq8ikWAM",
                chapter_snapshot_id="21m00Tcm4TlvDq8ikWAM",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.get(
            project_id, chapter_id, chapter_snapshot_id, request_options=request_options
        )
        return _response.data

    async def stream(
        self,
        project_id: str,
        chapter_id: str,
        chapter_snapshot_id: str,
        *,
        convert_to_mpeg: typing.Optional[bool] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> typing.AsyncIterator[bytes]:
        """
        Stream the audio from a chapter snapshot. Use `GET /v1/studio/projects/{project_id}/chapters/{chapter_id}/snapshots` to return the snapshots of a chapter.

        Parameters
        ----------
        project_id : str
            The ID of the project to be used. You can use the [List projects](/docs/api-reference/studio/get-projects) endpoint to list all the available projects.

        chapter_id : str
            The ID of the chapter to be used. You can use the [List project chapters](/docs/api-reference/studio/get-chapters) endpoint to list all the available chapters.

        chapter_snapshot_id : str
            The ID of the chapter snapshot to be used. You can use the [List project chapter snapshots](/docs/api-reference/studio/get-snapshots) endpoint to list all the available snapshots.

        convert_to_mpeg : typing.Optional[bool]
            Whether to convert the audio to mpeg format.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration. You can pass in configuration such as `chunk_size`, and more to customize the request and response.

        Returns
        -------
        typing.AsyncIterator[bytes]
            Streaming audio data
        """
        async with self._raw_client.stream(
            project_id,
            chapter_id,
            chapter_snapshot_id,
            convert_to_mpeg=convert_to_mpeg,
            request_options=request_options,
        ) as r:
            async for _chunk in r.data:
                yield _chunk

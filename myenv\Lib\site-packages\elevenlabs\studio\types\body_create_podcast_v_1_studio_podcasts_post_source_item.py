# This file was auto-generated by <PERSON><PERSON> from our API Definition.

from __future__ import annotations

import typing

import pydantic
import typing_extensions
from ...core.pydantic_utilities import IS_PYDANTIC_V2
from ...core.unchecked_base_model import UncheckedBaseModel, UnionMetadata


class BodyCreatePodcastV1StudioPodcastsPostSourceItem_Text(UncheckedBaseModel):
    type: typing.Literal["text"] = "text"
    text: str

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow


class BodyCreatePodcastV1StudioPodcastsPostSourceItem_Url(UncheckedBaseModel):
    type: typing.Literal["url"] = "url"
    url: str

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow


BodyCreatePodcastV1StudioPodcastsPostSourceItem = typing_extensions.Annotated[
    typing.Union[
        BodyCreatePodcastV1StudioPodcastsPostSourceItem_Text, BodyCreatePodcastV1StudioPodcastsPostSourceItem_Url
    ],
    UnionMetadata(discriminant="type"),
]

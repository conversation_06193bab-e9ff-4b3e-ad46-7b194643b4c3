# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

from ...core.client_wrapper import <PERSON>ync<PERSON><PERSON><PERSON>rap<PERSON>, SyncClientWrapper
from ...core.request_options import RequestOptions
from ...types.get_tool_dependent_agents_response_model import GetToolDependentAgentsResponseModel
from ...types.tool_request_model import ToolRequestModel
from ...types.tool_response_model import ToolResponseModel
from ...types.tools_response_model import ToolsResponseModel
from .raw_client import AsyncRawToolsClient, RawToolsClient

# this is used as the default value for optional parameters
OMIT = typing.cast(typing.Any, ...)


class ToolsClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._raw_client = RawToolsClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> RawToolsClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        RawToolsClient
        """
        return self._raw_client

    def list(self, *, request_options: typing.Optional[RequestOptions] = None) -> ToolsResponseModel:
        """
        Get all available tools available in the workspace.

        Parameters
        ----------
        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        ToolsResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.conversational_ai.tools.list()
        """
        _response = self._raw_client.list(request_options=request_options)
        return _response.data

    def create(
        self, *, request: ToolRequestModel, request_options: typing.Optional[RequestOptions] = None
    ) -> ToolResponseModel:
        """
        Add a new tool to the available tools in the workspace.

        Parameters
        ----------
        request : ToolRequestModel

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        ToolResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import (
            ElevenLabs,
            ToolRequestModel,
            ToolRequestModelToolConfig_Client,
        )

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.conversational_ai.tools.create(
            request=ToolRequestModel(
                tool_config=ToolRequestModelToolConfig_Client(
                    name="name",
                    description="description",
                    expects_response=False,
                ),
            ),
        )
        """
        _response = self._raw_client.create(request=request, request_options=request_options)
        return _response.data

    def get(self, tool_id: str, *, request_options: typing.Optional[RequestOptions] = None) -> ToolResponseModel:
        """
        Get tool that is available in the workspace.

        Parameters
        ----------
        tool_id : str
            ID of the requested tool.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        ToolResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.conversational_ai.tools.get(
            tool_id="tool_id",
        )
        """
        _response = self._raw_client.get(tool_id, request_options=request_options)
        return _response.data

    def delete(
        self, tool_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> typing.Optional[typing.Any]:
        """
        Delete tool from the workspace.

        Parameters
        ----------
        tool_id : str
            ID of the requested tool.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.Optional[typing.Any]
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.conversational_ai.tools.delete(
            tool_id="tool_id",
        )
        """
        _response = self._raw_client.delete(tool_id, request_options=request_options)
        return _response.data

    def update(
        self, tool_id: str, *, request: ToolRequestModel, request_options: typing.Optional[RequestOptions] = None
    ) -> ToolResponseModel:
        """
        Update tool that is available in the workspace.

        Parameters
        ----------
        tool_id : str
            ID of the requested tool.

        request : ToolRequestModel

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        ToolResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import (
            ElevenLabs,
            ToolRequestModel,
            ToolRequestModelToolConfig_Client,
        )

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.conversational_ai.tools.update(
            tool_id="tool_id",
            request=ToolRequestModel(
                tool_config=ToolRequestModelToolConfig_Client(
                    name="name",
                    description="description",
                    expects_response=False,
                ),
            ),
        )
        """
        _response = self._raw_client.update(tool_id, request=request, request_options=request_options)
        return _response.data

    def get_dependent_agents(
        self,
        tool_id: str,
        *,
        cursor: typing.Optional[str] = None,
        page_size: typing.Optional[int] = None,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> GetToolDependentAgentsResponseModel:
        """
        Get a list of agents depending on this tool

        Parameters
        ----------
        tool_id : str
            ID of the requested tool.

        cursor : typing.Optional[str]
            Used for fetching next page. Cursor is returned in the response.

        page_size : typing.Optional[int]
            How many documents to return at maximum. Can not exceed 100, defaults to 30.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        GetToolDependentAgentsResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.conversational_ai.tools.get_dependent_agents(
            tool_id="tool_id",
        )
        """
        _response = self._raw_client.get_dependent_agents(
            tool_id, cursor=cursor, page_size=page_size, request_options=request_options
        )
        return _response.data


class AsyncToolsClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._raw_client = AsyncRawToolsClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> AsyncRawToolsClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        AsyncRawToolsClient
        """
        return self._raw_client

    async def list(self, *, request_options: typing.Optional[RequestOptions] = None) -> ToolsResponseModel:
        """
        Get all available tools available in the workspace.

        Parameters
        ----------
        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        ToolsResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.conversational_ai.tools.list()


        asyncio.run(main())
        """
        _response = await self._raw_client.list(request_options=request_options)
        return _response.data

    async def create(
        self, *, request: ToolRequestModel, request_options: typing.Optional[RequestOptions] = None
    ) -> ToolResponseModel:
        """
        Add a new tool to the available tools in the workspace.

        Parameters
        ----------
        request : ToolRequestModel

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        ToolResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import (
            AsyncElevenLabs,
            ToolRequestModel,
            ToolRequestModelToolConfig_Client,
        )

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.conversational_ai.tools.create(
                request=ToolRequestModel(
                    tool_config=ToolRequestModelToolConfig_Client(
                        name="name",
                        description="description",
                        expects_response=False,
                    ),
                ),
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.create(request=request, request_options=request_options)
        return _response.data

    async def get(self, tool_id: str, *, request_options: typing.Optional[RequestOptions] = None) -> ToolResponseModel:
        """
        Get tool that is available in the workspace.

        Parameters
        ----------
        tool_id : str
            ID of the requested tool.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        ToolResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.conversational_ai.tools.get(
                tool_id="tool_id",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.get(tool_id, request_options=request_options)
        return _response.data

    async def delete(
        self, tool_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> typing.Optional[typing.Any]:
        """
        Delete tool from the workspace.

        Parameters
        ----------
        tool_id : str
            ID of the requested tool.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.Optional[typing.Any]
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.conversational_ai.tools.delete(
                tool_id="tool_id",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.delete(tool_id, request_options=request_options)
        return _response.data

    async def update(
        self, tool_id: str, *, request: ToolRequestModel, request_options: typing.Optional[RequestOptions] = None
    ) -> ToolResponseModel:
        """
        Update tool that is available in the workspace.

        Parameters
        ----------
        tool_id : str
            ID of the requested tool.

        request : ToolRequestModel

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        ToolResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import (
            AsyncElevenLabs,
            ToolRequestModel,
            ToolRequestModelToolConfig_Client,
        )

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.conversational_ai.tools.update(
                tool_id="tool_id",
                request=ToolRequestModel(
                    tool_config=ToolRequestModelToolConfig_Client(
                        name="name",
                        description="description",
                        expects_response=False,
                    ),
                ),
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.update(tool_id, request=request, request_options=request_options)
        return _response.data

    async def get_dependent_agents(
        self,
        tool_id: str,
        *,
        cursor: typing.Optional[str] = None,
        page_size: typing.Optional[int] = None,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> GetToolDependentAgentsResponseModel:
        """
        Get a list of agents depending on this tool

        Parameters
        ----------
        tool_id : str
            ID of the requested tool.

        cursor : typing.Optional[str]
            Used for fetching next page. Cursor is returned in the response.

        page_size : typing.Optional[int]
            How many documents to return at maximum. Can not exceed 100, defaults to 30.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        GetToolDependentAgentsResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.conversational_ai.tools.get_dependent_agents(
                tool_id="tool_id",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.get_dependent_agents(
            tool_id, cursor=cursor, page_size=page_size, request_options=request_options
        )
        return _response.data

{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/cloud-platform": {"description": "See, edit, configure, and delete your Google Cloud data and see the email address for your Google Account."}}}}, "basePath": "", "baseUrl": "https://apphub.googleapis.com/", "batchPath": "batch", "canonicalName": "App <PERSON>b", "description": "", "discoveryVersion": "v1", "documentationLink": "https://cloud.google.com/app-hub/docs/", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "apphub:v1", "kind": "discovery#restDescription", "mtlsRootUrl": "https://apphub.mtls.googleapis.com/", "name": "apphub", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"projects": {"resources": {"locations": {"methods": {"detachServiceProjectAttachment": {"description": "Detaches a service project from a host project. You can call this API from any service project without needing access to the host project that it is attached to.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}:detachServiceProjectAttachment", "httpMethod": "POST", "id": "apphub.projects.locations.detachServiceProjectAttachment", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Service project id and location to detach from a host project. Only global location is supported. Expected format: `projects/{project}/locations/{location}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:detachServiceProjectAttachment", "request": {"$ref": "DetachServiceProjectAttachmentRequest"}, "response": {"$ref": "DetachServiceProjectAttachmentResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets information about a location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}", "httpMethod": "GET", "id": "apphub.projects.locations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Resource name for the location.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Location"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists information about the supported locations for this service.", "flatPath": "v1/projects/{projectsId}/locations", "httpMethod": "GET", "id": "apphub.projects.locations.list", "parameterOrder": ["name"], "parameters": {"extraLocationTypes": {"description": "Optional. A list of extra location types that should be used as conditions for controlling the visibility of the locations.", "location": "query", "repeated": true, "type": "string"}, "filter": {"description": "A filter to narrow down results to a preferred subset. The filtering language accepts strings like `\"displayName=tokyo\"`, and is documented in more detail in [AIP-160](https://google.aip.dev/160).", "location": "query", "type": "string"}, "name": {"description": "The resource that owns the locations collection, if applicable.", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "The maximum number of results to return. If not set, the service selects a default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token received from the `next_page_token` field in the response. Send that page token to receive the subsequent page.", "location": "query", "type": "string"}}, "path": "v1/{+name}/locations", "response": {"$ref": "ListLocationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "lookupServiceProjectAttachment": {"description": "Lists a service project attachment for a given service project. You can call this API from any project to find if it is attached to a host project.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}:lookupServiceProjectAttachment", "httpMethod": "GET", "id": "apphub.projects.locations.lookupServiceProjectAttachment", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Service project ID and location to lookup service project attachment for. Only global location is supported. Expected format: `projects/{project}/locations/{location}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:lookupServiceProjectAttachment", "response": {"$ref": "LookupServiceProjectAttachmentResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"applications": {"methods": {"create": {"description": "Creates an Application in a host project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/applications", "httpMethod": "POST", "id": "apphub.projects.locations.applications.create", "parameterOrder": ["parent"], "parameters": {"applicationId": {"description": "Required. The Application identifier. Must contain only lowercase letters, numbers or hyphens, with the first character a letter, the last a letter or a number, and a 63 character maximum.", "location": "query", "type": "string"}, "parent": {"description": "Required. Project and location to create Application in. Expected format: `projects/{project}/locations/{location}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}}, "path": "v1/{+parent}/applications", "request": {"$ref": "Application"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes an Application in a host project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/applications/{applicationsId}", "httpMethod": "DELETE", "id": "apphub.projects.locations.applications.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Fully qualified name of the Application to delete. Expected format: `projects/{project}/locations/{location}/applications/{application}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/applications/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets an Application in a host project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/applications/{applicationsId}", "httpMethod": "GET", "id": "apphub.projects.locations.applications.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Fully qualified name of the Application to fetch. Expected format: `projects/{project}/locations/{location}/applications/{application}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/applications/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Application"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getIamPolicy": {"description": "Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/applications/{applicationsId}:getIamPolicy", "httpMethod": "GET", "id": "apphub.projects.locations.applications.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"options.requestedPolicyVersion": {"description": "Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "location": "query", "type": "integer"}, "resource": {"description": "REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/applications/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:getIamPolicy", "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists Applications in a host project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/applications", "httpMethod": "GET", "id": "apphub.projects.locations.applications.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. Filtering results.", "location": "query", "type": "string"}, "orderBy": {"description": "Optional. Hint for how to order the results.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. Requested page size. Server may return fewer items than requested. If unspecified, server will pick an appropriate default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A token identifying a page of results the server should return.", "location": "query", "type": "string"}, "parent": {"description": "Required. Project and location to list Applications on. Expected format: `projects/{project}/locations/{location}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/applications", "response": {"$ref": "ListApplicationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates an Application in a host project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/applications/{applicationsId}", "httpMethod": "PATCH", "id": "apphub.projects.locations.applications.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Identifier. The resource name of an Application. Format: `\"projects/{host-project-id}/locations/{location}/applications/{application-id}\"`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/applications/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "updateMask": {"description": "Required. Field mask is used to specify the fields to be overwritten in the Application resource by the update. The fields specified in the update_mask are relative to the resource, not the full request. The API changes the values of the fields as specified in the update_mask. The API ignores the values of all fields not covered by the update_mask. You can also unset a field by not specifying it in the updated message, but adding the field to the mask. This clears whatever value the field previously had.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "Application"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "setIamPolicy": {"description": "Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/applications/{applicationsId}:setIamPolicy", "httpMethod": "POST", "id": "apphub.projects.locations.applications.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/applications/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:setIamPolicy", "request": {"$ref": "SetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "testIamPermissions": {"description": "Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may \"fail open\" without warning.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/applications/{applicationsId}:testIamPermissions", "httpMethod": "POST", "id": "apphub.projects.locations.applications.testIamPermissions", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/applications/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:testIamPermissions", "request": {"$ref": "TestIamPermissionsRequest"}, "response": {"$ref": "TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"services": {"methods": {"create": {"description": "Creates a Service in an Application.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/applications/{applicationsId}/services", "httpMethod": "POST", "id": "apphub.projects.locations.applications.services.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. Fully qualified name of the parent Application to create the Service in. Expected format: `projects/{project}/locations/{location}/applications/{application}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/applications/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "serviceId": {"description": "Required. The Service identifier. Must contain only lowercase letters, numbers or hyphens, with the first character a letter, the last a letter or a number, and a 63 character maximum.", "location": "query", "type": "string"}}, "path": "v1/{+parent}/services", "request": {"$ref": "Service"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a Service from an Application.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/applications/{applicationsId}/services/{servicesId}", "httpMethod": "DELETE", "id": "apphub.projects.locations.applications.services.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Fully qualified name of the Service to delete from an Application. Expected format: `projects/{project}/locations/{location}/applications/{application}/services/{service}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/applications/[^/]+/services/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets a Service in an Application.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/applications/{applicationsId}/services/{servicesId}", "httpMethod": "GET", "id": "apphub.projects.locations.applications.services.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Fully qualified name of the Service to fetch. Expected format: `projects/{project}/locations/{location}/applications/{application}/services/{service}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/applications/[^/]+/services/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Service"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists Services in an Application.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/applications/{applicationsId}/services", "httpMethod": "GET", "id": "apphub.projects.locations.applications.services.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. Filtering results", "location": "query", "type": "string"}, "orderBy": {"description": "Optional. Hint for how to order the results", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. Requested page size. Server may return fewer items than requested. If unspecified, server will pick an appropriate default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A token identifying a page of results the server should return.", "location": "query", "type": "string"}, "parent": {"description": "Required. Fully qualified name of the parent Application to list Services for. Expected format: `projects/{project}/locations/{location}/applications/{application}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/applications/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/services", "response": {"$ref": "ListServicesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates a Service in an Application.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/applications/{applicationsId}/services/{servicesId}", "httpMethod": "PATCH", "id": "apphub.projects.locations.applications.services.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Identifier. The resource name of a Service. Format: `\"projects/{host-project-id}/locations/{location}/applications/{application-id}/services/{service-id}\"`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/applications/[^/]+/services/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "updateMask": {"description": "Required. Field mask is used to specify the fields to be overwritten in the Service resource by the update. The fields specified in the update_mask are relative to the resource, not the full request. The API changes the values of the fields as specified in the update_mask. The API ignores the values of all fields not covered by the update_mask. You can also unset a field by not specifying it in the updated message, but adding the field to the mask. This clears whatever value the field previously had.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "Service"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "workloads": {"methods": {"create": {"description": "Creates a Workload in an Application.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/applications/{applicationsId}/workloads", "httpMethod": "POST", "id": "apphub.projects.locations.applications.workloads.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. Fully qualified name of the Application to create Workload in. Expected format: `projects/{project}/locations/{location}/applications/{application}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/applications/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "workloadId": {"description": "Required. The Workload identifier. Must contain only lowercase letters, numbers or hyphens, with the first character a letter, the last a letter or a number, and a 63 character maximum.", "location": "query", "type": "string"}}, "path": "v1/{+parent}/workloads", "request": {"$ref": "Workload"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a Workload from an Application.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/applications/{applicationsId}/workloads/{workloadsId}", "httpMethod": "DELETE", "id": "apphub.projects.locations.applications.workloads.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Fully qualified name of the Workload to delete from an Application. Expected format: `projects/{project}/locations/{location}/applications/{application}/workloads/{workload}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/applications/[^/]+/workloads/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets a Workload in an Application.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/applications/{applicationsId}/workloads/{workloadsId}", "httpMethod": "GET", "id": "apphub.projects.locations.applications.workloads.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Fully qualified name of the Workload to fetch. Expected format: `projects/{project}/locations/{location}/applications/{application}/workloads/{workload}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/applications/[^/]+/workloads/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Workload"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists Workloads in an Application.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/applications/{applicationsId}/workloads", "httpMethod": "GET", "id": "apphub.projects.locations.applications.workloads.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. Filtering results.", "location": "query", "type": "string"}, "orderBy": {"description": "Optional. Hint for how to order the results.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. Requested page size. Server may return fewer items than requested. If unspecified, server will pick an appropriate default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A token identifying a page of results the server should return.", "location": "query", "type": "string"}, "parent": {"description": "Required. Fully qualified name of the parent Application to list Workloads for. Expected format: `projects/{project}/locations/{location}/applications/{application}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/applications/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/workloads", "response": {"$ref": "ListWorkloadsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates a Workload in an Application.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/applications/{applicationsId}/workloads/{workloadsId}", "httpMethod": "PATCH", "id": "apphub.projects.locations.applications.workloads.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Identifier. The resource name of the Workload. Format: `\"projects/{host-project-id}/locations/{location}/applications/{application-id}/workloads/{workload-id}\"`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/applications/[^/]+/workloads/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "updateMask": {"description": "Required. Field mask is used to specify the fields to be overwritten in the Workload resource by the update. The fields specified in the update_mask are relative to the resource, not the full request. The API changes the values of the fields as specified in the update_mask. The API ignores the values of all fields not covered by the update_mask. You can also unset a field by not specifying it in the updated message, but adding the field to the mask. This clears whatever value the field previously had.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "Workload"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}, "discoveredServices": {"methods": {"get": {"description": "Gets a Discovered Service in a host project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/discoveredServices/{discoveredServicesId}", "httpMethod": "GET", "id": "apphub.projects.locations.discoveredServices.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Fully qualified name of the Discovered Service to fetch. Expected format: `projects/{project}/locations/{location}/discoveredServices/{discoveredService}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/discoveredServices/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "DiscoveredService"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists Discovered Services that can be added to an Application in a host project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/discoveredServices", "httpMethod": "GET", "id": "apphub.projects.locations.discoveredServices.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. Filtering results.", "location": "query", "type": "string"}, "orderBy": {"description": "Optional. Hint for how to order the results.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. Requested page size. Server may return fewer items than requested. If unspecified, server will pick an appropriate default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A token identifying a page of results the server should return.", "location": "query", "type": "string"}, "parent": {"description": "Required. Project and location to list Discovered Services on. Expected format: `projects/{project}/locations/{location}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/discoveredServices", "response": {"$ref": "ListDiscoveredServicesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "lookup": {"description": "Lists a Discovered Service in a host project and location, with a given resource URI.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/discoveredServices:lookup", "httpMethod": "GET", "id": "apphub.projects.locations.discoveredServices.lookup", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. Host project ID and location to lookup Discovered Service in. Expected format: `projects/{project}/locations/{location}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "uri": {"description": "Required. Resource URI to find DiscoveredService for. Accepts both project number and project ID and does translation when needed.", "location": "query", "type": "string"}}, "path": "v1/{+parent}/discoveredServices:lookup", "response": {"$ref": "LookupDiscoveredServiceResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "discoveredWorkloads": {"methods": {"get": {"description": "Gets a Discovered Workload in a host project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/discoveredWorkloads/{discoveredWorkloadsId}", "httpMethod": "GET", "id": "apphub.projects.locations.discoveredWorkloads.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Fully qualified name of the Discovered Workload to fetch. Expected format: `projects/{project}/locations/{location}/discoveredWorkloads/{discoveredWorkload}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/discoveredWorkloads/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "DiscoveredWorkload"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists Discovered Workloads that can be added to an Application in a host project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/discoveredWorkloads", "httpMethod": "GET", "id": "apphub.projects.locations.discoveredWorkloads.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. Filtering results.", "location": "query", "type": "string"}, "orderBy": {"description": "Optional. Hint for how to order the results.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. Requested page size. Server may return fewer items than requested. If unspecified, server will pick an appropriate default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A token identifying a page of results the server should return.", "location": "query", "type": "string"}, "parent": {"description": "Required. Project and location to list Discovered Workloads on. Expected format: `projects/{project}/locations/{location}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/discoveredWorkloads", "response": {"$ref": "ListDiscoveredWorkloadsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "lookup": {"description": "Lists a Discovered Workload in a host project and location, with a given resource URI.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/discoveredWorkloads:lookup", "httpMethod": "GET", "id": "apphub.projects.locations.discoveredWorkloads.lookup", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. Host project ID and location to lookup Discovered Workload in. Expected format: `projects/{project}/locations/{location}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "uri": {"description": "Required. Resource URI to find Discovered Workload for. Accepts both project number and project ID and does translation when needed.", "location": "query", "type": "string"}}, "path": "v1/{+parent}/discoveredWorkloads:lookup", "response": {"$ref": "LookupDiscoveredWorkloadResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "operations": {"methods": {"cancel": {"description": "Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}:cancel", "httpMethod": "POST", "id": "apphub.projects.locations.operations.cancel", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource to be cancelled.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:cancel", "request": {"$ref": "CancelOperationRequest"}, "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}", "httpMethod": "DELETE", "id": "apphub.projects.locations.operations.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource to be deleted.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}", "httpMethod": "GET", "id": "apphub.projects.locations.operations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/operations", "httpMethod": "GET", "id": "apphub.projects.locations.operations.list", "parameterOrder": ["name"], "parameters": {"filter": {"description": "The standard list filter.", "location": "query", "type": "string"}, "name": {"description": "The name of the operation's parent resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "The standard list page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The standard list page token.", "location": "query", "type": "string"}}, "path": "v1/{+name}/operations", "response": {"$ref": "ListOperationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "serviceProjectAttachments": {"methods": {"create": {"description": "Attaches a service project to the host project.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/serviceProjectAttachments", "httpMethod": "POST", "id": "apphub.projects.locations.serviceProjectAttachments.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. Host project ID and location to which service project is being attached. Only global location is supported. Expected format: `projects/{project}/locations/{location}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "serviceProjectAttachmentId": {"description": "Required. The service project attachment identifier must contain the project id of the service project specified in the service_project_attachment.service_project field.", "location": "query", "type": "string"}}, "path": "v1/{+parent}/serviceProjectAttachments", "request": {"$ref": "ServiceProjectAttachment"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a service project attachment.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/serviceProjectAttachments/{serviceProjectAttachmentsId}", "httpMethod": "DELETE", "id": "apphub.projects.locations.serviceProjectAttachments.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Fully qualified name of the service project attachment to delete. Expected format: `projects/{project}/locations/{location}/serviceProjectAttachments/{serviceProjectAttachment}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/serviceProjectAttachments/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets a service project attachment.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/serviceProjectAttachments/{serviceProjectAttachmentsId}", "httpMethod": "GET", "id": "apphub.projects.locations.serviceProjectAttachments.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Fully qualified name of the service project attachment to retrieve. Expected format: `projects/{project}/locations/{location}/serviceProjectAttachments/{serviceProjectAttachment}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/serviceProjectAttachments/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "ServiceProjectAttachment"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists service projects attached to the host project.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/serviceProjectAttachments", "httpMethod": "GET", "id": "apphub.projects.locations.serviceProjectAttachments.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. Filtering results.", "location": "query", "type": "string"}, "orderBy": {"description": "Optional. Hint for how to order the results.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. Requested page size. Server may return fewer items than requested. If unspecified, server will pick an appropriate default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A token identifying a page of results the server should return.", "location": "query", "type": "string"}, "parent": {"description": "Required. Host project ID and location to list service project attachments. Only global location is supported. Expected format: `projects/{project}/locations/{location}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/serviceProjectAttachments", "response": {"$ref": "ListServiceProjectAttachmentsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}}}}, "revision": "20250414", "rootUrl": "https://apphub.googleapis.com/", "schemas": {"Application": {"description": "Application defines the governance boundary for App Hub entities that perform a logical end-to-end business function. App Hub supports application level IAM permission to align with governance requirements.", "id": "Application", "properties": {"attributes": {"$ref": "Attributes", "description": "Optional. Consumer provided attributes."}, "createTime": {"description": "Output only. Create time.", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "Optional. User-defined description of an Application. Can have a maximum length of 2048 characters.", "type": "string"}, "displayName": {"description": "Optional. User-defined name for the Application. Can have a maximum length of 63 characters.", "type": "string"}, "name": {"description": "Identifier. The resource name of an Application. Format: `\"projects/{host-project-id}/locations/{location}/applications/{application-id}\"`", "type": "string"}, "scope": {"$ref": "<PERSON><PERSON>", "description": "Required. Immutable. Defines what data can be included into this Application. Limits which Services and Workloads can be registered."}, "state": {"description": "Output only. Application state.", "enum": ["STATE_UNSPECIFIED", "CREATING", "ACTIVE", "DELETING"], "enumDescriptions": ["Unspecified state.", "The Application is being created.", "The Application is ready to register Services and Workloads.", "The Application is being deleted."], "readOnly": true, "type": "string"}, "uid": {"description": "Output only. A universally unique identifier (in UUID4 format) for the `Application`.", "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. Update time.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "Attributes": {"description": "Consumer provided attributes.", "id": "Attributes", "properties": {"businessOwners": {"description": "Optional. Business team that ensures user needs are met and value is delivered", "items": {"$ref": "ContactInfo"}, "type": "array"}, "criticality": {"$ref": "Criticality", "description": "Optional. User-defined criticality information."}, "developerOwners": {"description": "Optional. Developer team that owns development and coding.", "items": {"$ref": "ContactInfo"}, "type": "array"}, "environment": {"$ref": "Environment", "description": "Optional. User-defined environment information."}, "operatorOwners": {"description": "Optional. Operator team that ensures runtime and operations.", "items": {"$ref": "ContactInfo"}, "type": "array"}}, "type": "object"}, "AuditConfig": {"description": "Specifies the audit configuration for a service. The configuration determines which permission types are logged, and what identities, if any, are exempted from logging. An AuditConfig must have one or more AuditLogConfigs. If there are AuditConfigs for both `allServices` and a specific service, the union of the two AuditConfigs is used for that service: the log_types specified in each AuditConfig are enabled, and the exempted_members in each AuditLogConfig are exempted. Example Policy with multiple AuditConfigs: { \"audit_configs\": [ { \"service\": \"allServices\", \"audit_log_configs\": [ { \"log_type\": \"DATA_READ\", \"exempted_members\": [ \"user:<EMAIL>\" ] }, { \"log_type\": \"DATA_WRITE\" }, { \"log_type\": \"ADMIN_READ\" } ] }, { \"service\": \"sampleservice.googleapis.com\", \"audit_log_configs\": [ { \"log_type\": \"DATA_READ\" }, { \"log_type\": \"DATA_WRITE\", \"exempted_members\": [ \"user:<EMAIL>\" ] } ] } ] } For sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ logging. It also exempts `<EMAIL>` from DATA_READ logging, and `<EMAIL>` from DATA_WRITE logging.", "id": "AuditConfig", "properties": {"auditLogConfigs": {"description": "The configuration for logging of each type of permission.", "items": {"$ref": "AuditLogConfig"}, "type": "array"}, "service": {"description": "Specifies a service that will be enabled for audit logging. For example, `storage.googleapis.com`, `cloudsql.googleapis.com`. `allServices` is a special value that covers all services.", "type": "string"}}, "type": "object"}, "AuditLogConfig": {"description": "Provides the configuration for logging a type of permissions. Example: { \"audit_log_configs\": [ { \"log_type\": \"DATA_READ\", \"exempted_members\": [ \"user:<EMAIL>\" ] }, { \"log_type\": \"DATA_WRITE\" } ] } This enables 'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from DATA_READ logging.", "id": "AuditLogConfig", "properties": {"exemptedMembers": {"description": "Specifies the identities that do not cause logging for this type of permission. Follows the same format of Binding.members.", "items": {"type": "string"}, "type": "array"}, "logType": {"description": "The log type that this config enables.", "enum": ["LOG_TYPE_UNSPECIFIED", "ADMIN_READ", "DATA_WRITE", "DATA_READ"], "enumDescriptions": ["Default case. Should never be this.", "Admin reads. Example: CloudIAM getIamPolicy", "Data writes. Example: CloudSQL Users create", "Data reads. Example: CloudSQL Users list"], "type": "string"}}, "type": "object"}, "Binding": {"description": "Associates `members`, or principals, with a `role`.", "id": "Binding", "properties": {"condition": {"$ref": "Expr", "description": "The condition that is associated with this binding. If the condition evaluates to `true`, then this binding applies to the current request. If the condition evaluates to `false`, then this binding does not apply to the current request. However, a different role binding might grant the same role to one or more of the principals in this binding. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies)."}, "members": {"description": "Specifies the principals requesting access for a Google Cloud resource. `members` can have the following values: * `allUsers`: A special identifier that represents anyone who is on the internet; with or without a Google account. * `allAuthenticatedUsers`: A special identifier that represents anyone who is authenticated with a Google account or a service account. Does not include identities that come from external identity providers (IdPs) through identity federation. * `user:{emailid}`: An email address that represents a specific Google account. For example, `<EMAIL>` . * `serviceAccount:{emailid}`: An email address that represents a Google service account. For example, `<EMAIL>`. * `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`: An identifier for a [Kubernetes service account](https://cloud.google.com/kubernetes-engine/docs/how-to/kubernetes-service-accounts). For example, `my-project.svc.id.goog[my-namespace/my-kubernetes-sa]`. * `group:{emailid}`: An email address that represents a Google group. For example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain (primary) that represents all the users of that domain. For example, `google.com` or `example.com`. * `principal://iam.googleapis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: A single identity in a workforce identity pool. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/group/{group_id}`: All workforce identities in a group. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/attribute.{attribute_name}/{attribute_value}`: All workforce identities with a specific attribute value. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/*`: All identities in a workforce identity pool. * `principal://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/subject/{subject_attribute_value}`: A single identity in a workload identity pool. * `principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/group/{group_id}`: A workload identity pool group. * `principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/attribute.{attribute_name}/{attribute_value}`: All identities in a workload identity pool with a certain attribute. * `principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/*`: All identities in a workload identity pool. * `deleted:user:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a user that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the user is recovered, this value reverts to `user:{emailid}` and the recovered user retains the role in the binding. * `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a service account that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the service account is undeleted, this value reverts to `serviceAccount:{emailid}` and the undeleted service account retains the role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a Google group that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the group is recovered, this value reverts to `group:{emailid}` and the recovered group retains the role in the binding. * `deleted:principal://iam.googleapis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: Deleted single identity in a workforce identity pool. For example, `deleted:principal://iam.googleapis.com/locations/global/workforcePools/my-pool-id/subject/my-subject-attribute-value`.", "items": {"type": "string"}, "type": "array"}, "role": {"description": "Role that is assigned to the list of `members`, or principals. For example, `roles/viewer`, `roles/editor`, or `roles/owner`. For an overview of the IAM roles and permissions, see the [IAM documentation](https://cloud.google.com/iam/docs/roles-overview). For a list of the available pre-defined roles, see [here](https://cloud.google.com/iam/docs/understanding-roles).", "type": "string"}}, "type": "object"}, "CancelOperationRequest": {"description": "The request message for Operations.CancelOperation.", "id": "CancelOperationRequest", "properties": {}, "type": "object"}, "ContactInfo": {"description": "Contact information of stakeholders.", "id": "ContactInfo", "properties": {"displayName": {"description": "Optional. Contact's name. Can have a maximum length of 63 characters.", "type": "string"}, "email": {"description": "Required. Email address of the contacts.", "type": "string"}}, "type": "object"}, "Criticality": {"description": "Criticality of the Application, Service, or Workload", "id": "Criticality", "properties": {"type": {"description": "Required. Criticality Type.", "enum": ["TYPE_UNSPECIFIED", "MISSION_CRITICAL", "HIGH", "MEDIUM", "LOW"], "enumDescriptions": ["Unspecified type.", "Mission critical service, application or workload.", "High impact.", "Medium impact.", "Low impact."], "type": "string"}}, "type": "object"}, "DetachServiceProjectAttachmentRequest": {"description": "Request for DetachServiceProjectAttachment.", "id": "DetachServiceProjectAttachmentRequest", "properties": {}, "type": "object"}, "DetachServiceProjectAttachmentResponse": {"description": "Response for DetachServiceProjectAttachment.", "id": "DetachServiceProjectAttachmentResponse", "properties": {}, "type": "object"}, "DiscoveredService": {"description": "DiscoveredService is a network or API interface that exposes some functionality to clients for consumption over the network. A discovered service can be registered to a App Hub service.", "id": "DiscoveredService", "properties": {"name": {"description": "Identifier. The resource name of the discovered service. Format: `\"projects/{host-project-id}/locations/{location}/discoveredServices/{uuid}\"`", "type": "string"}, "serviceProperties": {"$ref": "ServiceProperties", "description": "Output only. Properties of an underlying compute resource that can comprise a Service. These are immutable.", "readOnly": true}, "serviceReference": {"$ref": "ServiceReference", "description": "Output only. Reference to an underlying networking resource that can comprise a Service. These are immutable.", "readOnly": true}}, "type": "object"}, "DiscoveredWorkload": {"description": "DiscoveredWorkload is a binary deployment (such as managed instance groups (MIGs) and GKE deployments) that performs the smallest logical subset of business functionality. A discovered workload can be registered to an App Hub Workload.", "id": "DiscoveredWorkload", "properties": {"name": {"description": "Identifier. The resource name of the discovered workload. Format: `\"projects/{host-project-id}/locations/{location}/discoveredWorkloads/{uuid}\"`", "type": "string"}, "workloadProperties": {"$ref": "WorkloadProperties", "description": "Output only. Properties of an underlying compute resource represented by the Workload. These are immutable.", "readOnly": true}, "workloadReference": {"$ref": "WorkloadReference", "description": "Output only. Reference of an underlying compute resource represented by the Workload. These are immutable.", "readOnly": true}}, "type": "object"}, "Empty": {"description": "A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }", "id": "Empty", "properties": {}, "type": "object"}, "Environment": {"description": "Environment of the Application, Service, or Workload", "id": "Environment", "properties": {"type": {"description": "Required. Environment Type.", "enum": ["TYPE_UNSPECIFIED", "PRODUCTION", "STAGING", "TEST", "DEVELOPMENT"], "enumDescriptions": ["Unspecified type.", "Production environment.", "Staging environment.", "Test environment.", "Development environment."], "type": "string"}}, "type": "object"}, "Expr": {"description": "Represents a textual expression in the Common Expression Language (CEL) syntax. CEL is a C-like expression language. The syntax and semantics of CEL are documented at https://github.com/google/cel-spec. Example (Comparison): title: \"Summary size limit\" description: \"Determines if a summary is less than 100 chars\" expression: \"document.summary.size() < 100\" Example (Equality): title: \"Requestor is owner\" description: \"Determines if requestor is the document owner\" expression: \"document.owner == request.auth.claims.email\" Example (Logic): title: \"Public documents\" description: \"Determine whether the document should be publicly visible\" expression: \"document.type != 'private' && document.type != 'internal'\" Example (Data Manipulation): title: \"Notification string\" description: \"Create a notification string with a timestamp.\" expression: \"'New message received at ' + string(document.create_time)\" The exact variables and functions that may be referenced within an expression are determined by the service that evaluates it. See the service documentation for additional information.", "id": "Expr", "properties": {"description": {"description": "Optional. Description of the expression. This is a longer text which describes the expression, e.g. when hovered over it in a UI.", "type": "string"}, "expression": {"description": "Textual representation of an expression in Common Expression Language syntax.", "type": "string"}, "location": {"description": "Optional. String indicating the location of the expression for error reporting, e.g. a file name and a position in the file.", "type": "string"}, "title": {"description": "Optional. Title for the expression, i.e. a short string describing its purpose. This can be used e.g. in UIs which allow to enter the expression.", "type": "string"}}, "type": "object"}, "ListApplicationsResponse": {"description": "Response for ListApplications.", "id": "ListApplicationsResponse", "properties": {"applications": {"description": "List of Applications.", "items": {"$ref": "Application"}, "type": "array"}, "nextPageToken": {"description": "A token identifying a page of results the server should return.", "type": "string"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListDiscoveredServicesResponse": {"description": "Response for ListDiscoveredServices.", "id": "ListDiscoveredServicesResponse", "properties": {"discoveredServices": {"description": "List of Discovered Services.", "items": {"$ref": "DiscoveredService"}, "type": "array"}, "nextPageToken": {"description": "A token identifying a page of results the server should return.", "type": "string"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListDiscoveredWorkloadsResponse": {"description": "Response for ListDiscoveredWorkloads.", "id": "ListDiscoveredWorkloadsResponse", "properties": {"discoveredWorkloads": {"description": "List of Discovered Workloads.", "items": {"$ref": "DiscoveredWorkload"}, "type": "array"}, "nextPageToken": {"description": "A token identifying a page of results the server should return.", "type": "string"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListLocationsResponse": {"description": "The response message for Locations.ListLocations.", "id": "ListLocationsResponse", "properties": {"locations": {"description": "A list of locations that matches the specified filter in the request.", "items": {"$ref": "Location"}, "type": "array"}, "nextPageToken": {"description": "The standard List next-page token.", "type": "string"}}, "type": "object"}, "ListOperationsResponse": {"description": "The response message for Operations.ListOperations.", "id": "ListOperationsResponse", "properties": {"nextPageToken": {"description": "The standard List next-page token.", "type": "string"}, "operations": {"description": "A list of operations that matches the specified filter in the request.", "items": {"$ref": "Operation"}, "type": "array"}}, "type": "object"}, "ListServiceProjectAttachmentsResponse": {"description": "Response for ListServiceProjectAttachments.", "id": "ListServiceProjectAttachmentsResponse", "properties": {"nextPageToken": {"description": "A token identifying a page of results the server should return.", "type": "string"}, "serviceProjectAttachments": {"description": "List of service project attachments.", "items": {"$ref": "ServiceProjectAttachment"}, "type": "array"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListServicesResponse": {"description": "Response for ListServices.", "id": "ListServicesResponse", "properties": {"nextPageToken": {"description": "A token identifying a page of results the server should return.", "type": "string"}, "services": {"description": "List of Services.", "items": {"$ref": "Service"}, "type": "array"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListWorkloadsResponse": {"description": "Response for ListWorkloads.", "id": "ListWorkloadsResponse", "properties": {"nextPageToken": {"description": "A token identifying a page of results the server should return.", "type": "string"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}, "workloads": {"description": "List of Workloads.", "items": {"$ref": "Workload"}, "type": "array"}}, "type": "object"}, "Location": {"description": "A resource that represents a Google Cloud location.", "id": "Location", "properties": {"displayName": {"description": "The friendly name for this location, typically a nearby city name. For example, \"Tokyo\".", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Cross-service attributes for the location. For example {\"cloud.googleapis.com/region\": \"us-east1\"}", "type": "object"}, "locationId": {"description": "The canonical id for this location. For example: `\"us-east1\"`.", "type": "string"}, "metadata": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "Service-specific metadata. For example the available capacity at the given location.", "type": "object"}, "name": {"description": "Resource name for the location, which may vary between implementations. For example: `\"projects/example-project/locations/us-east1\"`", "type": "string"}}, "type": "object"}, "LookupDiscoveredServiceResponse": {"description": "Response for LookupDiscoveredService.", "id": "LookupDiscoveredServiceResponse", "properties": {"discoveredService": {"$ref": "DiscoveredService", "description": "Discovered Service if exists, empty otherwise."}}, "type": "object"}, "LookupDiscoveredWorkloadResponse": {"description": "Response for LookupDiscoveredWorkload.", "id": "LookupDiscoveredWorkloadResponse", "properties": {"discoveredWorkload": {"$ref": "DiscoveredWorkload", "description": "Discovered Workload if exists, empty otherwise."}}, "type": "object"}, "LookupServiceProjectAttachmentResponse": {"description": "Response for LookupServiceProjectAttachment.", "id": "LookupServiceProjectAttachmentResponse", "properties": {"serviceProjectAttachment": {"$ref": "ServiceProjectAttachment", "description": "Service project attachment for a project if exists, empty otherwise."}}, "type": "object"}, "Operation": {"description": "This resource represents a long-running operation that is the result of a network API call.", "id": "Operation", "properties": {"done": {"description": "If the value is `false`, it means the operation is still in progress. If `true`, the operation is completed, and either `error` or `response` is available.", "type": "boolean"}, "error": {"$ref": "Status", "description": "The error result of the operation in case of failure or cancellation."}, "metadata": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "Service-specific metadata associated with the operation. It typically contains progress information and common metadata such as create time. Some services might not provide such metadata. Any method that returns a long-running operation should document the metadata type, if any.", "type": "object"}, "name": {"description": "The server-assigned name, which is only unique within the same service that originally returns it. If you use the default HTTP mapping, the `name` should be a resource name ending with `operations/{unique_id}`.", "type": "string"}, "response": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "The normal, successful response of the operation. If the original method returns no data on success, such as `Delete`, the response is `google.protobuf.Empty`. If the original method is standard `Get`/`Create`/`Update`, the response should be the resource. For other methods, the response should have the type `XxxResponse`, where `Xxx` is the original method name. For example, if the original method name is `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.", "type": "object"}}, "type": "object"}, "OperationMetadata": {"description": "Represents the metadata of the long-running operation.", "id": "OperationMetadata", "properties": {"apiVersion": {"description": "Output only. API version used to start the operation.", "readOnly": true, "type": "string"}, "createTime": {"description": "Output only. The time the operation was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "endTime": {"description": "Output only. The time the operation finished running.", "format": "google-datetime", "readOnly": true, "type": "string"}, "requestedCancellation": {"description": "Output only. Identifies whether the user has requested cancellation of the operation. Operations that have been cancelled successfully have google.longrunning.Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.", "readOnly": true, "type": "boolean"}, "statusMessage": {"description": "Output only. Human-readable status of the operation, if any.", "readOnly": true, "type": "string"}, "target": {"description": "Output only. Server-defined resource path for the target of the operation.", "readOnly": true, "type": "string"}, "verb": {"description": "Output only. Name of the verb executed by the operation.", "readOnly": true, "type": "string"}}, "type": "object"}, "Policy": {"description": "An Identity and Access Management (IAM) policy, which specifies access controls for Google Cloud resources. A `Policy` is a collection of `bindings`. A `binding` binds one or more `members`, or principals, to a single `role`. Principals can be user accounts, service accounts, Google groups, and domains (such as G Suite). A `role` is a named list of permissions; each `role` can be an IAM predefined role or a user-created custom role. For some types of Google Cloud resources, a `binding` can also specify a `condition`, which is a logical expression that allows access to a resource only if the expression evaluates to `true`. A condition can add constraints based on attributes of the request, the resource, or both. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies). **JSON example:** ``` { \"bindings\": [ { \"role\": \"roles/resourcemanager.organizationAdmin\", \"members\": [ \"user:<EMAIL>\", \"group:<EMAIL>\", \"domain:google.com\", \"serviceAccount:<EMAIL>\" ] }, { \"role\": \"roles/resourcemanager.organizationViewer\", \"members\": [ \"user:<EMAIL>\" ], \"condition\": { \"title\": \"expirable access\", \"description\": \"Does not grant access after Sep 2020\", \"expression\": \"request.time < timestamp('2020-10-01T00:00:00.000Z')\", } } ], \"etag\": \"BwWWja0YfJA=\", \"version\": 3 } ``` **YAML example:** ``` bindings: - members: - user:<EMAIL> - group:<EMAIL> - domain:google.com - serviceAccount:<EMAIL> role: roles/resourcemanager.organizationAdmin - members: - user:<EMAIL> role: roles/resourcemanager.organizationViewer condition: title: expirable access description: Does not grant access after Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z') etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features, see the [IAM documentation](https://cloud.google.com/iam/docs/).", "id": "Policy", "properties": {"auditConfigs": {"description": "Specifies cloud audit logging configuration for this policy.", "items": {"$ref": "AuditConfig"}, "type": "array"}, "bindings": {"description": "Associates a list of `members`, or principals, with a `role`. Optionally, may specify a `condition` that determines how and when the `bindings` are applied. Each of the `bindings` must contain at least one principal. The `bindings` in a `Policy` can refer to up to 1,500 principals; up to 250 of these principals can be Google groups. Each occurrence of a principal counts towards these limits. For example, if the `bindings` grant 50 different roles to `user:<EMAIL>`, and not to any other principal, then you can add another 1,450 principals to the `bindings` in the `Policy`.", "items": {"$ref": "Binding"}, "type": "array"}, "etag": {"description": "`etag` is used for optimistic concurrency control as a way to help prevent simultaneous updates of a policy from overwriting each other. It is strongly suggested that systems make use of the `etag` in the read-modify-write cycle to perform policy updates in order to avoid race conditions: An `etag` is returned in the response to `getIamPolicy`, and systems are expected to put that etag in the request to `setIamPolicy` to ensure that their change will be applied to the same version of the policy. **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost.", "format": "byte", "type": "string"}, "version": {"description": "Specifies the format of the policy. Valid values are `0`, `1`, and `3`. Requests that specify an invalid value are rejected. Any operation that affects conditional role bindings must specify version `3`. This requirement applies to the following operations: * Getting a policy that includes a conditional role binding * Adding a conditional role binding to a policy * Changing a conditional role binding in a policy * Removing any role binding, with or without a condition, from a policy that includes conditions **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost. If a policy does not include any conditions, operations on that policy may specify any valid version or leave the field unset. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "type": "integer"}}, "type": "object"}, "ReconciliationOperationMetadata": {"description": "Operation metadata returned by the CLH during resource state reconciliation.", "id": "ReconciliationOperationMetadata", "properties": {"deleteResource": {"deprecated": true, "description": "DEPRECATED. Use exclusive_action instead.", "type": "boolean"}, "exclusiveAction": {"description": "Excluisive action returned by the CLH.", "enum": ["UNKNOWN_REPAIR_ACTION", "DELETE", "RETRY"], "enumDeprecated": [false, true, false], "enumDescriptions": ["Unknown repair action.", "The resource has to be deleted. When using this bit, the CLH should fail the operation. DEPRECATED. Instead use DELETE_RESOURCE OperationSignal in SideChannel.", "This resource could not be repaired but the repair should be tried again at a later time. This can happen if there is a dependency that needs to be resolved first- e.g. if a parent resource must be repaired before a child resource."], "type": "string"}}, "type": "object"}, "Scope": {"description": "Scope of an application.", "id": "<PERSON><PERSON>", "properties": {"type": {"description": "Required. Scope Type.", "enum": ["TYPE_UNSPECIFIED", "REGIONAL", "GLOBAL"], "enumDescriptions": ["Unspecified type.", "Regional type.", "Global type."], "type": "string"}}, "type": "object"}, "Service": {"description": "Service is an App Hub data model that contains a discovered service, which represents a network or API interface that exposes some functionality to clients for consumption over the network.", "id": "Service", "properties": {"attributes": {"$ref": "Attributes", "description": "Optional. Consumer provided attributes."}, "createTime": {"description": "Output only. Create time.", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "Optional. User-defined description of a Service. Can have a maximum length of 2048 characters.", "type": "string"}, "discoveredService": {"description": "Required. Immutable. The resource name of the original discovered service.", "type": "string"}, "displayName": {"description": "Optional. User-defined name for the Service. Can have a maximum length of 63 characters.", "type": "string"}, "name": {"description": "Identifier. The resource name of a Service. Format: `\"projects/{host-project-id}/locations/{location}/applications/{application-id}/services/{service-id}\"`", "type": "string"}, "serviceProperties": {"$ref": "ServiceProperties", "description": "Output only. Properties of an underlying compute resource that can comprise a Service. These are immutable.", "readOnly": true}, "serviceReference": {"$ref": "ServiceReference", "description": "Output only. Reference to an underlying networking resource that can comprise a Service. These are immutable.", "readOnly": true}, "state": {"description": "Output only. Service state.", "enum": ["STATE_UNSPECIFIED", "CREATING", "ACTIVE", "DELETING", "DETACHED"], "enumDescriptions": ["Unspecified state.", "The service is being created.", "The service is ready.", "The service is being deleted.", "The underlying networking resources have been deleted."], "readOnly": true, "type": "string"}, "uid": {"description": "Output only. A universally unique identifier (UUID) for the `Service` in the UUID4 format.", "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. Update time.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "ServiceProjectAttachment": {"description": "ServiceProjectAttachment represents an attachment from a service project to a host project. Service projects contain the underlying cloud infrastructure resources, and expose these resources to the host project through a ServiceProjectAttachment. With the attachments, the host project can provide an aggregated view of resources across all service projects.", "id": "ServiceProjectAttachment", "properties": {"createTime": {"description": "Output only. Create time.", "format": "google-datetime", "readOnly": true, "type": "string"}, "name": {"description": "Identifier. The resource name of a ServiceProjectAttachment. Format: `\"projects/{host-project-id}/locations/global/serviceProjectAttachments/{service-project-id}.\"`", "type": "string"}, "serviceProject": {"description": "Required. Immutable. Service project name in the format: `\"projects/abc\"` or `\"projects/123\"`. As input, project name with either project id or number are accepted. As output, this field will contain project number.", "type": "string"}, "state": {"description": "Output only. ServiceProjectAttachment state.", "enum": ["STATE_UNSPECIFIED", "CREATING", "ACTIVE", "DELETING"], "enumDescriptions": ["Unspecified state.", "The ServiceProjectAttachment is being created.", "The ServiceProjectAttachment is ready. This means Services and Workloads under the corresponding ServiceProjectAttachment is ready for registration.", "The ServiceProjectAttachment is being deleted."], "readOnly": true, "type": "string"}, "uid": {"description": "Output only. A globally unique identifier (in UUID4 format) for the `ServiceProjectAttachment`.", "readOnly": true, "type": "string"}}, "type": "object"}, "ServiceProperties": {"description": "Properties of an underlying cloud resource that can comprise a Service.", "id": "ServiceProperties", "properties": {"gcpProject": {"description": "Output only. The service project identifier that the underlying cloud resource resides in.", "readOnly": true, "type": "string"}, "location": {"description": "Output only. The location that the underlying resource resides in, for example, us-west1.", "readOnly": true, "type": "string"}, "zone": {"description": "Output only. The location that the underlying resource resides in if it is zonal, for example, us-west1-a).", "readOnly": true, "type": "string"}}, "type": "object"}, "ServiceReference": {"description": "Reference to an underlying networking resource that can comprise a Service.", "id": "ServiceReference", "properties": {"uri": {"description": "Output only. The underlying resource URI. For example, URI of Forwarding Rule, URL Map, and Backend Service.", "readOnly": true, "type": "string"}}, "type": "object"}, "SetIamPolicyRequest": {"description": "Request message for `SetIamPolicy` method.", "id": "SetIamPolicyRequest", "properties": {"policy": {"$ref": "Policy", "description": "REQUIRED: The complete policy to be applied to the `resource`. The size of the policy is limited to a few 10s of KB. An empty policy is a valid policy but certain Google Cloud services (such as Projects) might reject them."}, "updateMask": {"description": "OPTIONAL: A FieldMask specifying which fields of the policy to modify. Only the fields in the mask will be modified. If no mask is provided, the following default mask is used: `paths: \"bindings, etag\"`", "format": "google-fieldmask", "type": "string"}}, "type": "object"}, "Status": {"description": "The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).", "id": "Status", "properties": {"code": {"description": "The status code, which should be an enum value of google.rpc.Code.", "format": "int32", "type": "integer"}, "details": {"description": "A list of messages that carry the error details. There is a common set of message types for APIs to use.", "items": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "type": "object"}, "type": "array"}, "message": {"description": "A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the google.rpc.Status.details field, or localized by the client.", "type": "string"}}, "type": "object"}, "TestIamPermissionsRequest": {"description": "Request message for `TestIamPermissions` method.", "id": "TestIamPermissionsRequest", "properties": {"permissions": {"description": "The set of permissions to check for the `resource`. Permissions with wildcards (such as `*` or `storage.*`) are not allowed. For more information see [IAM Overview](https://cloud.google.com/iam/docs/overview#permissions).", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "TestIamPermissionsResponse": {"description": "Response message for `TestIamPermissions` method.", "id": "TestIamPermissionsResponse", "properties": {"permissions": {"description": "A subset of `TestPermissionsRequest.permissions` that the caller is allowed.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "Workload": {"description": "Workload is an App Hub data model that contains a discovered workload, which represents a binary deployment (such as managed instance groups (MIGs) and GKE deployments) that performs the smallest logical subset of business functionality.", "id": "Workload", "properties": {"attributes": {"$ref": "Attributes", "description": "Optional. Consumer provided attributes."}, "createTime": {"description": "Output only. Create time.", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "Optional. User-defined description of a Workload. Can have a maximum length of 2048 characters.", "type": "string"}, "discoveredWorkload": {"description": "Required. Immutable. The resource name of the original discovered workload.", "type": "string"}, "displayName": {"description": "Optional. User-defined name for the Workload. Can have a maximum length of 63 characters.", "type": "string"}, "name": {"description": "Identifier. The resource name of the Workload. Format: `\"projects/{host-project-id}/locations/{location}/applications/{application-id}/workloads/{workload-id}\"`", "type": "string"}, "state": {"description": "Output only. Workload state.", "enum": ["STATE_UNSPECIFIED", "CREATING", "ACTIVE", "DELETING", "DETACHED"], "enumDescriptions": ["Unspecified state.", "The Workload is being created.", "The Workload is ready.", "The Workload is being deleted.", "The underlying compute resources have been deleted."], "readOnly": true, "type": "string"}, "uid": {"description": "Output only. A universally unique identifier (UUID) for the `Workload` in the UUID4 format.", "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. Update time.", "format": "google-datetime", "readOnly": true, "type": "string"}, "workloadProperties": {"$ref": "WorkloadProperties", "description": "Output only. Properties of an underlying compute resource represented by the Workload. These are immutable.", "readOnly": true}, "workloadReference": {"$ref": "WorkloadReference", "description": "Output only. Reference of an underlying compute resource represented by the Workload. These are immutable.", "readOnly": true}}, "type": "object"}, "WorkloadProperties": {"description": "Properties of an underlying compute resource represented by the Workload.", "id": "WorkloadProperties", "properties": {"gcpProject": {"description": "Output only. The service project identifier that the underlying cloud resource resides in. Empty for non-cloud resources.", "readOnly": true, "type": "string"}, "location": {"description": "Output only. The location that the underlying compute resource resides in (for example, us-west1).", "readOnly": true, "type": "string"}, "zone": {"description": "Output only. The location that the underlying compute resource resides in if it is zonal (for example, us-west1-a).", "readOnly": true, "type": "string"}}, "type": "object"}, "WorkloadReference": {"description": "Reference of an underlying compute resource represented by the Workload.", "id": "WorkloadReference", "properties": {"uri": {"description": "Output only. The underlying compute resource uri.", "readOnly": true, "type": "string"}}, "type": "object"}}, "servicePath": "", "title": "App Hub API", "version": "v1", "version_module": true}
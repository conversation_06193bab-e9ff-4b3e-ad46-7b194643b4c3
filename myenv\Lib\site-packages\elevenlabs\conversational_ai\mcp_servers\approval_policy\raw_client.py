# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing
from json.decoder import JSONDecodeError

from ....core.api_error import ApiError
from ....core.client_wrapper import Async<PERSON><PERSON><PERSON>rapper, SyncClientWrapper
from ....core.http_response import AsyncHttpR<PERSON>ponse, HttpResponse
from ....core.jsonable_encoder import jsonable_encoder
from ....core.request_options import RequestOptions
from ....core.unchecked_base_model import construct_type
from ....errors.unprocessable_entity_error import UnprocessableEntityError
from ....types.http_validation_error import HttpValidationError
from ....types.mcp_approval_policy import McpApprovalPolicy
from ....types.mcp_server_response_model import McpServerResponseModel

# this is used as the default value for optional parameters
OMIT = typing.cast(typing.Any, ...)


class RawApprovalPolicyClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._client_wrapper = client_wrapper

    def update(
        self,
        mcp_server_id: str,
        *,
        approval_policy: McpApprovalPolicy,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> HttpResponse[McpServerResponseModel]:
        """
        Update the approval policy configuration for an MCP server.

        Parameters
        ----------
        mcp_server_id : str
            ID of the MCP Server.

        approval_policy : McpApprovalPolicy
            The approval mode to set for the MCP server

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        HttpResponse[McpServerResponseModel]
            Successful Response
        """
        _response = self._client_wrapper.httpx_client.request(
            f"v1/convai/mcp-servers/{jsonable_encoder(mcp_server_id)}/approval-policy",
            method="PATCH",
            json={
                "approval_policy": approval_policy,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                _data = typing.cast(
                    McpServerResponseModel,
                    construct_type(
                        type_=McpServerResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
                return HttpResponse(response=_response, data=_data)
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    headers=dict(_response.headers),
                    body=typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response.text)
        raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)


class AsyncRawApprovalPolicyClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._client_wrapper = client_wrapper

    async def update(
        self,
        mcp_server_id: str,
        *,
        approval_policy: McpApprovalPolicy,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> AsyncHttpResponse[McpServerResponseModel]:
        """
        Update the approval policy configuration for an MCP server.

        Parameters
        ----------
        mcp_server_id : str
            ID of the MCP Server.

        approval_policy : McpApprovalPolicy
            The approval mode to set for the MCP server

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AsyncHttpResponse[McpServerResponseModel]
            Successful Response
        """
        _response = await self._client_wrapper.httpx_client.request(
            f"v1/convai/mcp-servers/{jsonable_encoder(mcp_server_id)}/approval-policy",
            method="PATCH",
            json={
                "approval_policy": approval_policy,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                _data = typing.cast(
                    McpServerResponseModel,
                    construct_type(
                        type_=McpServerResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
                return AsyncHttpResponse(response=_response, data=_data)
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    headers=dict(_response.headers),
                    body=typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response.text)
        raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)

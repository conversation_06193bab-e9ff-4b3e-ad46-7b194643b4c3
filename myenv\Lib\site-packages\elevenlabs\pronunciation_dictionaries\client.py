# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

from .. import core
from ..core.client_wrapper import AsyncC<PERSON><PERSON>rapper, SyncClientWrapper
from ..core.request_options import RequestOptions
from ..types.add_pronunciation_dictionary_response_model import AddPronunciationDictionaryResponseModel
from ..types.get_pronunciation_dictionaries_metadata_response_model import (
    GetPronunciationDictionariesMetadataResponseModel,
)
from ..types.get_pronunciation_dictionary_metadata_response import GetPronunciationDictionaryMetadataResponse
from .raw_client import AsyncRawPronunciationDictionariesClient, RawPronunciationDictionariesClient
from .rules.client import AsyncRulesClient, RulesClient
from .types.body_add_a_pronunciation_dictionary_v_1_pronunciation_dictionaries_add_from_rules_post_rules_item import (
    BodyAddAPronunciationDictionaryV1PronunciationDictionariesAddFromRulesPostRulesItem,
)
from .types.body_add_a_pronunciation_dictionary_v_1_pronunciation_dictionaries_add_from_rules_post_workspace_access import (
    BodyAddAPronunciationDictionaryV1PronunciationDictionariesAddFromRulesPostWorkspaceAccess,
)
from .types.pronunciation_dictionaries_create_from_file_request_workspace_access import (
    PronunciationDictionariesCreateFromFileRequestWorkspaceAccess,
)
from .types.pronunciation_dictionaries_list_request_sort import PronunciationDictionariesListRequestSort

# this is used as the default value for optional parameters
OMIT = typing.cast(typing.Any, ...)


class PronunciationDictionariesClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._raw_client = RawPronunciationDictionariesClient(client_wrapper=client_wrapper)
        self.rules = RulesClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> RawPronunciationDictionariesClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        RawPronunciationDictionariesClient
        """
        return self._raw_client

    def create_from_file(
        self,
        *,
        name: str,
        file: typing.Optional[core.File] = OMIT,
        description: typing.Optional[str] = OMIT,
        workspace_access: typing.Optional[PronunciationDictionariesCreateFromFileRequestWorkspaceAccess] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> AddPronunciationDictionaryResponseModel:
        """
        Creates a new pronunciation dictionary from a lexicon .PLS file

        Parameters
        ----------
        name : str
            The name of the pronunciation dictionary, used for identification only.

        file : typing.Optional[core.File]
            See core.File for more documentation

        description : typing.Optional[str]
            A description of the pronunciation dictionary, used for identification only.

        workspace_access : typing.Optional[PronunciationDictionariesCreateFromFileRequestWorkspaceAccess]
            Should be one of 'admin', 'editor' or 'viewer'. If not provided, defaults to no access.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AddPronunciationDictionaryResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.pronunciation_dictionaries.create_from_file(
            name="name",
        )
        """
        _response = self._raw_client.create_from_file(
            name=name,
            file=file,
            description=description,
            workspace_access=workspace_access,
            request_options=request_options,
        )
        return _response.data

    def create_from_rules(
        self,
        *,
        rules: typing.Sequence[BodyAddAPronunciationDictionaryV1PronunciationDictionariesAddFromRulesPostRulesItem],
        name: str,
        description: typing.Optional[str] = OMIT,
        workspace_access: typing.Optional[
            BodyAddAPronunciationDictionaryV1PronunciationDictionariesAddFromRulesPostWorkspaceAccess
        ] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> AddPronunciationDictionaryResponseModel:
        """
        Creates a new pronunciation dictionary from provided rules.

        Parameters
        ----------
        rules : typing.Sequence[BodyAddAPronunciationDictionaryV1PronunciationDictionariesAddFromRulesPostRulesItem]
            List of pronunciation rules. Rule can be either:
                an alias rule: {'string_to_replace': 'a', 'type': 'alias', 'alias': 'b', }
                or a phoneme rule: {'string_to_replace': 'a', 'type': 'phoneme', 'phoneme': 'b', 'alphabet': 'ipa' }

        name : str
            The name of the pronunciation dictionary, used for identification only.

        description : typing.Optional[str]
            A description of the pronunciation dictionary, used for identification only.

        workspace_access : typing.Optional[BodyAddAPronunciationDictionaryV1PronunciationDictionariesAddFromRulesPostWorkspaceAccess]
            Should be one of 'admin', 'editor' or 'viewer'. If not provided, defaults to no access.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AddPronunciationDictionaryResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs
        from elevenlabs.pronunciation_dictionaries import (
            BodyAddAPronunciationDictionaryV1PronunciationDictionariesAddFromRulesPostRulesItem_Alias,
        )

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.pronunciation_dictionaries.create_from_rules(
            rules=[
                BodyAddAPronunciationDictionaryV1PronunciationDictionariesAddFromRulesPostRulesItem_Alias(
                    string_to_replace="Thailand",
                    alias="tie-land",
                )
            ],
            name="My Dictionary",
        )
        """
        _response = self._raw_client.create_from_rules(
            rules=rules,
            name=name,
            description=description,
            workspace_access=workspace_access,
            request_options=request_options,
        )
        return _response.data

    def download(
        self, dictionary_id: str, version_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> typing.Iterator[bytes]:
        """
        Get a PLS file with a pronunciation dictionary version rules

        Parameters
        ----------
        dictionary_id : str
            The id of the pronunciation dictionary

        version_id : str
            The id of the version of the pronunciation dictionary

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration. You can pass in configuration such as `chunk_size`, and more to customize the request and response.

        Returns
        -------
        typing.Iterator[bytes]
            The PLS file containing pronunciation dictionary rules
        """
        with self._raw_client.download(dictionary_id, version_id, request_options=request_options) as r:
            yield from r.data

    def get(
        self, pronunciation_dictionary_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> GetPronunciationDictionaryMetadataResponse:
        """
        Get metadata for a pronunciation dictionary

        Parameters
        ----------
        pronunciation_dictionary_id : str
            The id of the pronunciation dictionary

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        GetPronunciationDictionaryMetadataResponse
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.pronunciation_dictionaries.get(
            pronunciation_dictionary_id="21m00Tcm4TlvDq8ikWAM",
        )
        """
        _response = self._raw_client.get(pronunciation_dictionary_id, request_options=request_options)
        return _response.data

    def list(
        self,
        *,
        cursor: typing.Optional[str] = None,
        page_size: typing.Optional[int] = None,
        sort: typing.Optional[PronunciationDictionariesListRequestSort] = None,
        sort_direction: typing.Optional[str] = None,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> GetPronunciationDictionariesMetadataResponseModel:
        """
        Get a list of the pronunciation dictionaries you have access to and their metadata

        Parameters
        ----------
        cursor : typing.Optional[str]
            Used for fetching next page. Cursor is returned in the response.

        page_size : typing.Optional[int]
            How many pronunciation dictionaries to return at maximum. Can not exceed 100, defaults to 30.

        sort : typing.Optional[PronunciationDictionariesListRequestSort]
            Which field to sort by, one of 'created_at_unix' or 'name'.

        sort_direction : typing.Optional[str]
            Which direction to sort the voices in. 'ascending' or 'descending'.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        GetPronunciationDictionariesMetadataResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.pronunciation_dictionaries.list()
        """
        _response = self._raw_client.list(
            cursor=cursor,
            page_size=page_size,
            sort=sort,
            sort_direction=sort_direction,
            request_options=request_options,
        )
        return _response.data


class AsyncPronunciationDictionariesClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._raw_client = AsyncRawPronunciationDictionariesClient(client_wrapper=client_wrapper)
        self.rules = AsyncRulesClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> AsyncRawPronunciationDictionariesClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        AsyncRawPronunciationDictionariesClient
        """
        return self._raw_client

    async def create_from_file(
        self,
        *,
        name: str,
        file: typing.Optional[core.File] = OMIT,
        description: typing.Optional[str] = OMIT,
        workspace_access: typing.Optional[PronunciationDictionariesCreateFromFileRequestWorkspaceAccess] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> AddPronunciationDictionaryResponseModel:
        """
        Creates a new pronunciation dictionary from a lexicon .PLS file

        Parameters
        ----------
        name : str
            The name of the pronunciation dictionary, used for identification only.

        file : typing.Optional[core.File]
            See core.File for more documentation

        description : typing.Optional[str]
            A description of the pronunciation dictionary, used for identification only.

        workspace_access : typing.Optional[PronunciationDictionariesCreateFromFileRequestWorkspaceAccess]
            Should be one of 'admin', 'editor' or 'viewer'. If not provided, defaults to no access.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AddPronunciationDictionaryResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.pronunciation_dictionaries.create_from_file(
                name="name",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.create_from_file(
            name=name,
            file=file,
            description=description,
            workspace_access=workspace_access,
            request_options=request_options,
        )
        return _response.data

    async def create_from_rules(
        self,
        *,
        rules: typing.Sequence[BodyAddAPronunciationDictionaryV1PronunciationDictionariesAddFromRulesPostRulesItem],
        name: str,
        description: typing.Optional[str] = OMIT,
        workspace_access: typing.Optional[
            BodyAddAPronunciationDictionaryV1PronunciationDictionariesAddFromRulesPostWorkspaceAccess
        ] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> AddPronunciationDictionaryResponseModel:
        """
        Creates a new pronunciation dictionary from provided rules.

        Parameters
        ----------
        rules : typing.Sequence[BodyAddAPronunciationDictionaryV1PronunciationDictionariesAddFromRulesPostRulesItem]
            List of pronunciation rules. Rule can be either:
                an alias rule: {'string_to_replace': 'a', 'type': 'alias', 'alias': 'b', }
                or a phoneme rule: {'string_to_replace': 'a', 'type': 'phoneme', 'phoneme': 'b', 'alphabet': 'ipa' }

        name : str
            The name of the pronunciation dictionary, used for identification only.

        description : typing.Optional[str]
            A description of the pronunciation dictionary, used for identification only.

        workspace_access : typing.Optional[BodyAddAPronunciationDictionaryV1PronunciationDictionariesAddFromRulesPostWorkspaceAccess]
            Should be one of 'admin', 'editor' or 'viewer'. If not provided, defaults to no access.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AddPronunciationDictionaryResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs
        from elevenlabs.pronunciation_dictionaries import (
            BodyAddAPronunciationDictionaryV1PronunciationDictionariesAddFromRulesPostRulesItem_Alias,
        )

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.pronunciation_dictionaries.create_from_rules(
                rules=[
                    BodyAddAPronunciationDictionaryV1PronunciationDictionariesAddFromRulesPostRulesItem_Alias(
                        string_to_replace="Thailand",
                        alias="tie-land",
                    )
                ],
                name="My Dictionary",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.create_from_rules(
            rules=rules,
            name=name,
            description=description,
            workspace_access=workspace_access,
            request_options=request_options,
        )
        return _response.data

    async def download(
        self, dictionary_id: str, version_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> typing.AsyncIterator[bytes]:
        """
        Get a PLS file with a pronunciation dictionary version rules

        Parameters
        ----------
        dictionary_id : str
            The id of the pronunciation dictionary

        version_id : str
            The id of the version of the pronunciation dictionary

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration. You can pass in configuration such as `chunk_size`, and more to customize the request and response.

        Returns
        -------
        typing.AsyncIterator[bytes]
            The PLS file containing pronunciation dictionary rules
        """
        async with self._raw_client.download(dictionary_id, version_id, request_options=request_options) as r:
            async for _chunk in r.data:
                yield _chunk

    async def get(
        self, pronunciation_dictionary_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> GetPronunciationDictionaryMetadataResponse:
        """
        Get metadata for a pronunciation dictionary

        Parameters
        ----------
        pronunciation_dictionary_id : str
            The id of the pronunciation dictionary

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        GetPronunciationDictionaryMetadataResponse
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.pronunciation_dictionaries.get(
                pronunciation_dictionary_id="21m00Tcm4TlvDq8ikWAM",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.get(pronunciation_dictionary_id, request_options=request_options)
        return _response.data

    async def list(
        self,
        *,
        cursor: typing.Optional[str] = None,
        page_size: typing.Optional[int] = None,
        sort: typing.Optional[PronunciationDictionariesListRequestSort] = None,
        sort_direction: typing.Optional[str] = None,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> GetPronunciationDictionariesMetadataResponseModel:
        """
        Get a list of the pronunciation dictionaries you have access to and their metadata

        Parameters
        ----------
        cursor : typing.Optional[str]
            Used for fetching next page. Cursor is returned in the response.

        page_size : typing.Optional[int]
            How many pronunciation dictionaries to return at maximum. Can not exceed 100, defaults to 30.

        sort : typing.Optional[PronunciationDictionariesListRequestSort]
            Which field to sort by, one of 'created_at_unix' or 'name'.

        sort_direction : typing.Optional[str]
            Which direction to sort the voices in. 'ascending' or 'descending'.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        GetPronunciationDictionariesMetadataResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.pronunciation_dictionaries.list()


        asyncio.run(main())
        """
        _response = await self._raw_client.list(
            cursor=cursor,
            page_size=page_size,
            sort=sort,
            sort_direction=sort_direction,
            request_options=request_options,
        )
        return _response.data

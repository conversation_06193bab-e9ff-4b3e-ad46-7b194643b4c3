# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing
from json.decoder import JSONDecodeError

from ..... import core
from .....core.api_error import ApiError
from .....core.client_wrapper import Async<PERSON>lientWrapper, SyncClientWrapper
from .....core.http_response import AsyncHttpResponse, HttpResponse
from .....core.jsonable_encoder import jsonable_encoder
from .....core.request_options import RequestOptions
from .....core.unchecked_base_model import construct_type
from .....errors.unprocessable_entity_error import UnprocessableEntityError
from .....types.http_validation_error import HttpValidationError
from .....types.post_agent_avatar_response_model import PostAgentAvatarResponseModel

# this is used as the default value for optional parameters
OMIT = typing.cast(typing.Any, ...)


class RawAvatarClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._client_wrapper = client_wrapper

    def create(
        self, agent_id: str, *, avatar_file: core.File, request_options: typing.Optional[RequestOptions] = None
    ) -> HttpResponse[PostAgentAvatarResponseModel]:
        """
        Sets the avatar for an agent displayed in the widget

        Parameters
        ----------
        agent_id : str
            The id of an agent. This is returned on agent creation.

        avatar_file : core.File
            See core.File for more documentation

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        HttpResponse[PostAgentAvatarResponseModel]
            Successful Response
        """
        _response = self._client_wrapper.httpx_client.request(
            f"v1/convai/agents/{jsonable_encoder(agent_id)}/avatar",
            method="POST",
            data={},
            files={
                "avatar_file": avatar_file,
            },
            request_options=request_options,
            omit=OMIT,
            force_multipart=True,
        )
        try:
            if 200 <= _response.status_code < 300:
                _data = typing.cast(
                    PostAgentAvatarResponseModel,
                    construct_type(
                        type_=PostAgentAvatarResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
                return HttpResponse(response=_response, data=_data)
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    headers=dict(_response.headers),
                    body=typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response.text)
        raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)


class AsyncRawAvatarClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._client_wrapper = client_wrapper

    async def create(
        self, agent_id: str, *, avatar_file: core.File, request_options: typing.Optional[RequestOptions] = None
    ) -> AsyncHttpResponse[PostAgentAvatarResponseModel]:
        """
        Sets the avatar for an agent displayed in the widget

        Parameters
        ----------
        agent_id : str
            The id of an agent. This is returned on agent creation.

        avatar_file : core.File
            See core.File for more documentation

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AsyncHttpResponse[PostAgentAvatarResponseModel]
            Successful Response
        """
        _response = await self._client_wrapper.httpx_client.request(
            f"v1/convai/agents/{jsonable_encoder(agent_id)}/avatar",
            method="POST",
            data={},
            files={
                "avatar_file": avatar_file,
            },
            request_options=request_options,
            omit=OMIT,
            force_multipart=True,
        )
        try:
            if 200 <= _response.status_code < 300:
                _data = typing.cast(
                    PostAgentAvatarResponseModel,
                    construct_type(
                        type_=PostAgentAvatarResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
                return AsyncHttpResponse(response=_response, data=_data)
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    headers=dict(_response.headers),
                    body=typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response.text)
        raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)

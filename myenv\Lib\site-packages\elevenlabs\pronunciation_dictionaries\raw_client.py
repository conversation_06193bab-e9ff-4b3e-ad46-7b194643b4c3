# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import contextlib
import typing
from json.decoder import JSONDecodeError

from .. import core
from ..core.api_error import ApiError
from ..core.client_wrapper import AsyncClient<PERSON>rapper, SyncClientWrapper
from ..core.http_response import AsyncHttpResponse, HttpResponse
from ..core.jsonable_encoder import jsonable_encoder
from ..core.request_options import RequestOptions
from ..core.serialization import convert_and_respect_annotation_metadata
from ..core.unchecked_base_model import construct_type
from ..errors.unprocessable_entity_error import UnprocessableEntityError
from ..types.add_pronunciation_dictionary_response_model import AddPronunciationDictionaryResponseModel
from ..types.get_pronunciation_dictionaries_metadata_response_model import (
    GetPronunciationDictionariesMetadataResponseModel,
)
from ..types.get_pronunciation_dictionary_metadata_response import GetPronunciationDictionaryMetadataResponse
from ..types.http_validation_error import HttpValidationError
from .types.body_add_a_pronunciation_dictionary_v_1_pronunciation_dictionaries_add_from_rules_post_rules_item import (
    BodyAddAPronunciationDictionaryV1PronunciationDictionariesAddFromRulesPostRulesItem,
)
from .types.body_add_a_pronunciation_dictionary_v_1_pronunciation_dictionaries_add_from_rules_post_workspace_access import (
    BodyAddAPronunciationDictionaryV1PronunciationDictionariesAddFromRulesPostWorkspaceAccess,
)
from .types.pronunciation_dictionaries_create_from_file_request_workspace_access import (
    PronunciationDictionariesCreateFromFileRequestWorkspaceAccess,
)
from .types.pronunciation_dictionaries_list_request_sort import PronunciationDictionariesListRequestSort

# this is used as the default value for optional parameters
OMIT = typing.cast(typing.Any, ...)


class RawPronunciationDictionariesClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._client_wrapper = client_wrapper

    def create_from_file(
        self,
        *,
        name: str,
        file: typing.Optional[core.File] = OMIT,
        description: typing.Optional[str] = OMIT,
        workspace_access: typing.Optional[PronunciationDictionariesCreateFromFileRequestWorkspaceAccess] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> HttpResponse[AddPronunciationDictionaryResponseModel]:
        """
        Creates a new pronunciation dictionary from a lexicon .PLS file

        Parameters
        ----------
        name : str
            The name of the pronunciation dictionary, used for identification only.

        file : typing.Optional[core.File]
            See core.File for more documentation

        description : typing.Optional[str]
            A description of the pronunciation dictionary, used for identification only.

        workspace_access : typing.Optional[PronunciationDictionariesCreateFromFileRequestWorkspaceAccess]
            Should be one of 'admin', 'editor' or 'viewer'. If not provided, defaults to no access.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        HttpResponse[AddPronunciationDictionaryResponseModel]
            Successful Response
        """
        _response = self._client_wrapper.httpx_client.request(
            "v1/pronunciation-dictionaries/add-from-file",
            method="POST",
            data={
                "name": name,
                "description": description,
                "workspace_access": workspace_access,
            },
            files={
                **({"file": file} if file is not None else {}),
            },
            request_options=request_options,
            omit=OMIT,
            force_multipart=True,
        )
        try:
            if 200 <= _response.status_code < 300:
                _data = typing.cast(
                    AddPronunciationDictionaryResponseModel,
                    construct_type(
                        type_=AddPronunciationDictionaryResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
                return HttpResponse(response=_response, data=_data)
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    headers=dict(_response.headers),
                    body=typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response.text)
        raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)

    def create_from_rules(
        self,
        *,
        rules: typing.Sequence[BodyAddAPronunciationDictionaryV1PronunciationDictionariesAddFromRulesPostRulesItem],
        name: str,
        description: typing.Optional[str] = OMIT,
        workspace_access: typing.Optional[
            BodyAddAPronunciationDictionaryV1PronunciationDictionariesAddFromRulesPostWorkspaceAccess
        ] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> HttpResponse[AddPronunciationDictionaryResponseModel]:
        """
        Creates a new pronunciation dictionary from provided rules.

        Parameters
        ----------
        rules : typing.Sequence[BodyAddAPronunciationDictionaryV1PronunciationDictionariesAddFromRulesPostRulesItem]
            List of pronunciation rules. Rule can be either:
                an alias rule: {'string_to_replace': 'a', 'type': 'alias', 'alias': 'b', }
                or a phoneme rule: {'string_to_replace': 'a', 'type': 'phoneme', 'phoneme': 'b', 'alphabet': 'ipa' }

        name : str
            The name of the pronunciation dictionary, used for identification only.

        description : typing.Optional[str]
            A description of the pronunciation dictionary, used for identification only.

        workspace_access : typing.Optional[BodyAddAPronunciationDictionaryV1PronunciationDictionariesAddFromRulesPostWorkspaceAccess]
            Should be one of 'admin', 'editor' or 'viewer'. If not provided, defaults to no access.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        HttpResponse[AddPronunciationDictionaryResponseModel]
            Successful Response
        """
        _response = self._client_wrapper.httpx_client.request(
            "v1/pronunciation-dictionaries/add-from-rules",
            method="POST",
            json={
                "rules": convert_and_respect_annotation_metadata(
                    object_=rules,
                    annotation=typing.Sequence[
                        BodyAddAPronunciationDictionaryV1PronunciationDictionariesAddFromRulesPostRulesItem
                    ],
                    direction="write",
                ),
                "name": name,
                "description": description,
                "workspace_access": workspace_access,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                _data = typing.cast(
                    AddPronunciationDictionaryResponseModel,
                    construct_type(
                        type_=AddPronunciationDictionaryResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
                return HttpResponse(response=_response, data=_data)
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    headers=dict(_response.headers),
                    body=typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response.text)
        raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)

    @contextlib.contextmanager
    def download(
        self, dictionary_id: str, version_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> typing.Iterator[HttpResponse[typing.Iterator[bytes]]]:
        """
        Get a PLS file with a pronunciation dictionary version rules

        Parameters
        ----------
        dictionary_id : str
            The id of the pronunciation dictionary

        version_id : str
            The id of the version of the pronunciation dictionary

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration. You can pass in configuration such as `chunk_size`, and more to customize the request and response.

        Returns
        -------
        typing.Iterator[HttpResponse[typing.Iterator[bytes]]]
            The PLS file containing pronunciation dictionary rules
        """
        with self._client_wrapper.httpx_client.stream(
            f"v1/pronunciation-dictionaries/{jsonable_encoder(dictionary_id)}/{jsonable_encoder(version_id)}/download",
            method="GET",
            request_options=request_options,
        ) as _response:

            def _stream() -> HttpResponse[typing.Iterator[bytes]]:
                try:
                    if 200 <= _response.status_code < 300:
                        _chunk_size = request_options.get("chunk_size", 1024) if request_options is not None else 1024
                        return HttpResponse(
                            response=_response, data=(_chunk for _chunk in _response.iter_bytes(chunk_size=_chunk_size))
                        )
                    _response.read()
                    if _response.status_code == 422:
                        raise UnprocessableEntityError(
                            headers=dict(_response.headers),
                            body=typing.cast(
                                HttpValidationError,
                                construct_type(
                                    type_=HttpValidationError,  # type: ignore
                                    object_=_response.json(),
                                ),
                            ),
                        )
                    _response_json = _response.json()
                except JSONDecodeError:
                    raise ApiError(
                        status_code=_response.status_code, headers=dict(_response.headers), body=_response.text
                    )
                raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)

            yield _stream()

    def get(
        self, pronunciation_dictionary_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> HttpResponse[GetPronunciationDictionaryMetadataResponse]:
        """
        Get metadata for a pronunciation dictionary

        Parameters
        ----------
        pronunciation_dictionary_id : str
            The id of the pronunciation dictionary

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        HttpResponse[GetPronunciationDictionaryMetadataResponse]
            Successful Response
        """
        _response = self._client_wrapper.httpx_client.request(
            f"v1/pronunciation-dictionaries/{jsonable_encoder(pronunciation_dictionary_id)}",
            method="GET",
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                _data = typing.cast(
                    GetPronunciationDictionaryMetadataResponse,
                    construct_type(
                        type_=GetPronunciationDictionaryMetadataResponse,  # type: ignore
                        object_=_response.json(),
                    ),
                )
                return HttpResponse(response=_response, data=_data)
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    headers=dict(_response.headers),
                    body=typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response.text)
        raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)

    def list(
        self,
        *,
        cursor: typing.Optional[str] = None,
        page_size: typing.Optional[int] = None,
        sort: typing.Optional[PronunciationDictionariesListRequestSort] = None,
        sort_direction: typing.Optional[str] = None,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> HttpResponse[GetPronunciationDictionariesMetadataResponseModel]:
        """
        Get a list of the pronunciation dictionaries you have access to and their metadata

        Parameters
        ----------
        cursor : typing.Optional[str]
            Used for fetching next page. Cursor is returned in the response.

        page_size : typing.Optional[int]
            How many pronunciation dictionaries to return at maximum. Can not exceed 100, defaults to 30.

        sort : typing.Optional[PronunciationDictionariesListRequestSort]
            Which field to sort by, one of 'created_at_unix' or 'name'.

        sort_direction : typing.Optional[str]
            Which direction to sort the voices in. 'ascending' or 'descending'.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        HttpResponse[GetPronunciationDictionariesMetadataResponseModel]
            Successful Response
        """
        _response = self._client_wrapper.httpx_client.request(
            "v1/pronunciation-dictionaries",
            method="GET",
            params={
                "cursor": cursor,
                "page_size": page_size,
                "sort": sort,
                "sort_direction": sort_direction,
            },
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                _data = typing.cast(
                    GetPronunciationDictionariesMetadataResponseModel,
                    construct_type(
                        type_=GetPronunciationDictionariesMetadataResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
                return HttpResponse(response=_response, data=_data)
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    headers=dict(_response.headers),
                    body=typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response.text)
        raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)


class AsyncRawPronunciationDictionariesClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._client_wrapper = client_wrapper

    async def create_from_file(
        self,
        *,
        name: str,
        file: typing.Optional[core.File] = OMIT,
        description: typing.Optional[str] = OMIT,
        workspace_access: typing.Optional[PronunciationDictionariesCreateFromFileRequestWorkspaceAccess] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> AsyncHttpResponse[AddPronunciationDictionaryResponseModel]:
        """
        Creates a new pronunciation dictionary from a lexicon .PLS file

        Parameters
        ----------
        name : str
            The name of the pronunciation dictionary, used for identification only.

        file : typing.Optional[core.File]
            See core.File for more documentation

        description : typing.Optional[str]
            A description of the pronunciation dictionary, used for identification only.

        workspace_access : typing.Optional[PronunciationDictionariesCreateFromFileRequestWorkspaceAccess]
            Should be one of 'admin', 'editor' or 'viewer'. If not provided, defaults to no access.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AsyncHttpResponse[AddPronunciationDictionaryResponseModel]
            Successful Response
        """
        _response = await self._client_wrapper.httpx_client.request(
            "v1/pronunciation-dictionaries/add-from-file",
            method="POST",
            data={
                "name": name,
                "description": description,
                "workspace_access": workspace_access,
            },
            files={
                **({"file": file} if file is not None else {}),
            },
            request_options=request_options,
            omit=OMIT,
            force_multipart=True,
        )
        try:
            if 200 <= _response.status_code < 300:
                _data = typing.cast(
                    AddPronunciationDictionaryResponseModel,
                    construct_type(
                        type_=AddPronunciationDictionaryResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
                return AsyncHttpResponse(response=_response, data=_data)
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    headers=dict(_response.headers),
                    body=typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response.text)
        raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)

    async def create_from_rules(
        self,
        *,
        rules: typing.Sequence[BodyAddAPronunciationDictionaryV1PronunciationDictionariesAddFromRulesPostRulesItem],
        name: str,
        description: typing.Optional[str] = OMIT,
        workspace_access: typing.Optional[
            BodyAddAPronunciationDictionaryV1PronunciationDictionariesAddFromRulesPostWorkspaceAccess
        ] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> AsyncHttpResponse[AddPronunciationDictionaryResponseModel]:
        """
        Creates a new pronunciation dictionary from provided rules.

        Parameters
        ----------
        rules : typing.Sequence[BodyAddAPronunciationDictionaryV1PronunciationDictionariesAddFromRulesPostRulesItem]
            List of pronunciation rules. Rule can be either:
                an alias rule: {'string_to_replace': 'a', 'type': 'alias', 'alias': 'b', }
                or a phoneme rule: {'string_to_replace': 'a', 'type': 'phoneme', 'phoneme': 'b', 'alphabet': 'ipa' }

        name : str
            The name of the pronunciation dictionary, used for identification only.

        description : typing.Optional[str]
            A description of the pronunciation dictionary, used for identification only.

        workspace_access : typing.Optional[BodyAddAPronunciationDictionaryV1PronunciationDictionariesAddFromRulesPostWorkspaceAccess]
            Should be one of 'admin', 'editor' or 'viewer'. If not provided, defaults to no access.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AsyncHttpResponse[AddPronunciationDictionaryResponseModel]
            Successful Response
        """
        _response = await self._client_wrapper.httpx_client.request(
            "v1/pronunciation-dictionaries/add-from-rules",
            method="POST",
            json={
                "rules": convert_and_respect_annotation_metadata(
                    object_=rules,
                    annotation=typing.Sequence[
                        BodyAddAPronunciationDictionaryV1PronunciationDictionariesAddFromRulesPostRulesItem
                    ],
                    direction="write",
                ),
                "name": name,
                "description": description,
                "workspace_access": workspace_access,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                _data = typing.cast(
                    AddPronunciationDictionaryResponseModel,
                    construct_type(
                        type_=AddPronunciationDictionaryResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
                return AsyncHttpResponse(response=_response, data=_data)
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    headers=dict(_response.headers),
                    body=typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response.text)
        raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)

    @contextlib.asynccontextmanager
    async def download(
        self, dictionary_id: str, version_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> typing.AsyncIterator[AsyncHttpResponse[typing.AsyncIterator[bytes]]]:
        """
        Get a PLS file with a pronunciation dictionary version rules

        Parameters
        ----------
        dictionary_id : str
            The id of the pronunciation dictionary

        version_id : str
            The id of the version of the pronunciation dictionary

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration. You can pass in configuration such as `chunk_size`, and more to customize the request and response.

        Returns
        -------
        typing.AsyncIterator[AsyncHttpResponse[typing.AsyncIterator[bytes]]]
            The PLS file containing pronunciation dictionary rules
        """
        async with self._client_wrapper.httpx_client.stream(
            f"v1/pronunciation-dictionaries/{jsonable_encoder(dictionary_id)}/{jsonable_encoder(version_id)}/download",
            method="GET",
            request_options=request_options,
        ) as _response:

            async def _stream() -> AsyncHttpResponse[typing.AsyncIterator[bytes]]:
                try:
                    if 200 <= _response.status_code < 300:
                        _chunk_size = request_options.get("chunk_size", 1024) if request_options is not None else 1024
                        return AsyncHttpResponse(
                            response=_response,
                            data=(_chunk async for _chunk in _response.aiter_bytes(chunk_size=_chunk_size)),
                        )
                    await _response.aread()
                    if _response.status_code == 422:
                        raise UnprocessableEntityError(
                            headers=dict(_response.headers),
                            body=typing.cast(
                                HttpValidationError,
                                construct_type(
                                    type_=HttpValidationError,  # type: ignore
                                    object_=_response.json(),
                                ),
                            ),
                        )
                    _response_json = _response.json()
                except JSONDecodeError:
                    raise ApiError(
                        status_code=_response.status_code, headers=dict(_response.headers), body=_response.text
                    )
                raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)

            yield await _stream()

    async def get(
        self, pronunciation_dictionary_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> AsyncHttpResponse[GetPronunciationDictionaryMetadataResponse]:
        """
        Get metadata for a pronunciation dictionary

        Parameters
        ----------
        pronunciation_dictionary_id : str
            The id of the pronunciation dictionary

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AsyncHttpResponse[GetPronunciationDictionaryMetadataResponse]
            Successful Response
        """
        _response = await self._client_wrapper.httpx_client.request(
            f"v1/pronunciation-dictionaries/{jsonable_encoder(pronunciation_dictionary_id)}",
            method="GET",
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                _data = typing.cast(
                    GetPronunciationDictionaryMetadataResponse,
                    construct_type(
                        type_=GetPronunciationDictionaryMetadataResponse,  # type: ignore
                        object_=_response.json(),
                    ),
                )
                return AsyncHttpResponse(response=_response, data=_data)
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    headers=dict(_response.headers),
                    body=typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response.text)
        raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)

    async def list(
        self,
        *,
        cursor: typing.Optional[str] = None,
        page_size: typing.Optional[int] = None,
        sort: typing.Optional[PronunciationDictionariesListRequestSort] = None,
        sort_direction: typing.Optional[str] = None,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> AsyncHttpResponse[GetPronunciationDictionariesMetadataResponseModel]:
        """
        Get a list of the pronunciation dictionaries you have access to and their metadata

        Parameters
        ----------
        cursor : typing.Optional[str]
            Used for fetching next page. Cursor is returned in the response.

        page_size : typing.Optional[int]
            How many pronunciation dictionaries to return at maximum. Can not exceed 100, defaults to 30.

        sort : typing.Optional[PronunciationDictionariesListRequestSort]
            Which field to sort by, one of 'created_at_unix' or 'name'.

        sort_direction : typing.Optional[str]
            Which direction to sort the voices in. 'ascending' or 'descending'.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AsyncHttpResponse[GetPronunciationDictionariesMetadataResponseModel]
            Successful Response
        """
        _response = await self._client_wrapper.httpx_client.request(
            "v1/pronunciation-dictionaries",
            method="GET",
            params={
                "cursor": cursor,
                "page_size": page_size,
                "sort": sort,
                "sort_direction": sort_direction,
            },
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                _data = typing.cast(
                    GetPronunciationDictionariesMetadataResponseModel,
                    construct_type(
                        type_=GetPronunciationDictionariesMetadataResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
                return AsyncHttpResponse(response=_response, data=_data)
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    headers=dict(_response.headers),
                    body=typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response.text)
        raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)

# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

from ....core.client_wrapper import AsyncClient<PERSON>rapper, SyncClientWrapper
from ....core.request_options import RequestOptions
from ....types.list_mcp_tools_response_model import ListMcpToolsResponseModel
from .raw_client import AsyncRawToolsClient, RawToolsClient


class ToolsClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._raw_client = RawToolsClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> RawToolsClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        RawToolsClient
        """
        return self._raw_client

    def list(
        self, mcp_server_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> ListMcpToolsResponseModel:
        """
        Retrieve all tools available for a specific MCP server configuration.

        Parameters
        ----------
        mcp_server_id : str
            ID of the MCP Server.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        ListMcpToolsResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.conversational_ai.mcp_servers.tools.list(
            mcp_server_id="mcp_server_id",
        )
        """
        _response = self._raw_client.list(mcp_server_id, request_options=request_options)
        return _response.data


class AsyncToolsClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._raw_client = AsyncRawToolsClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> AsyncRawToolsClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        AsyncRawToolsClient
        """
        return self._raw_client

    async def list(
        self, mcp_server_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> ListMcpToolsResponseModel:
        """
        Retrieve all tools available for a specific MCP server configuration.

        Parameters
        ----------
        mcp_server_id : str
            ID of the MCP Server.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        ListMcpToolsResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.conversational_ai.mcp_servers.tools.list(
                mcp_server_id="mcp_server_id",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.list(mcp_server_id, request_options=request_options)
        return _response.data

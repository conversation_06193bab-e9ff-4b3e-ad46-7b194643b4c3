# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

from ....core.client_wrapper import AsyncClient<PERSON>rap<PERSON>, SyncClientWrapper
from ....core.request_options import RequestOptions
from ....types.mcp_approval_policy import McpApprovalPolicy
from ....types.mcp_server_response_model import McpServerResponseModel
from .raw_client import AsyncRawApprovalPolicyClient, RawApprovalPolicyClient

# this is used as the default value for optional parameters
OMIT = typing.cast(typing.Any, ...)


class ApprovalPolicyClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._raw_client = RawApprovalPolicyClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> RawApprovalPolicyClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        RawApprovalPolicyClient
        """
        return self._raw_client

    def update(
        self,
        mcp_server_id: str,
        *,
        approval_policy: McpApprovalPolicy,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> McpServerResponseModel:
        """
        Update the approval policy configuration for an MCP server.

        Parameters
        ----------
        mcp_server_id : str
            ID of the MCP Server.

        approval_policy : McpApprovalPolicy
            The approval mode to set for the MCP server

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        McpServerResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.conversational_ai.mcp_servers.approval_policy.update(
            mcp_server_id="mcp_server_id",
            approval_policy="auto_approve_all",
        )
        """
        _response = self._raw_client.update(
            mcp_server_id, approval_policy=approval_policy, request_options=request_options
        )
        return _response.data


class AsyncApprovalPolicyClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._raw_client = AsyncRawApprovalPolicyClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> AsyncRawApprovalPolicyClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        AsyncRawApprovalPolicyClient
        """
        return self._raw_client

    async def update(
        self,
        mcp_server_id: str,
        *,
        approval_policy: McpApprovalPolicy,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> McpServerResponseModel:
        """
        Update the approval policy configuration for an MCP server.

        Parameters
        ----------
        mcp_server_id : str
            ID of the MCP Server.

        approval_policy : McpApprovalPolicy
            The approval mode to set for the MCP server

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        McpServerResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.conversational_ai.mcp_servers.approval_policy.update(
                mcp_server_id="mcp_server_id",
                approval_policy="auto_approve_all",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.update(
            mcp_server_id, approval_policy=approval_policy, request_options=request_options
        )
        return _response.data

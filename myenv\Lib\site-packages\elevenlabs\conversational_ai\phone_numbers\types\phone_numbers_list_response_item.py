# This file was auto-generated by <PERSON><PERSON> from our API Definition.

from __future__ import annotations

import typing

import pydantic
import typing_extensions
from ....core.pydantic_utilities import IS_PYDANTIC_V2
from ....core.unchecked_base_model import UncheckedBaseModel, UnionMetadata
from ....types.get_phone_number_inbound_sip_trunk_config_response_model import (
    GetPhoneNumberInboundSipTrunkConfigResponseModel,
)
from ....types.get_phone_number_outbound_sip_trunk_config_response_model import (
    GetPhoneNumberOutboundSipTrunkConfigResponseModel,
)
from ....types.phone_number_agent_info import PhoneNumberAgentInfo


class PhoneNumbersListResponseItem_Twilio(UncheckedBaseModel):
    provider: typing.Literal["twilio"] = "twilio"
    phone_number: str
    label: str
    supports_inbound: typing.Optional[bool] = None
    supports_outbound: typing.Optional[bool] = None
    phone_number_id: str
    assigned_agent: typing.Optional[PhoneNumberAgentInfo] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow


class PhoneNumbersListResponseItem_SipTrunk(UncheckedBaseModel):
    provider: typing.Literal["sip_trunk"] = "sip_trunk"
    phone_number: str
    label: str
    supports_inbound: typing.Optional[bool] = None
    supports_outbound: typing.Optional[bool] = None
    phone_number_id: str
    assigned_agent: typing.Optional[PhoneNumberAgentInfo] = None
    provider_config: typing.Optional[GetPhoneNumberOutboundSipTrunkConfigResponseModel] = None
    outbound_trunk: typing.Optional[GetPhoneNumberOutboundSipTrunkConfigResponseModel] = None
    inbound_trunk: typing.Optional[GetPhoneNumberInboundSipTrunkConfigResponseModel] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow


PhoneNumbersListResponseItem = typing_extensions.Annotated[
    typing.Union[PhoneNumbersListResponseItem_Twilio, PhoneNumbersListResponseItem_SipTrunk],
    UnionMetadata(discriminant="provider"),
]

# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

from ...core.client_wrapper import Async<PERSON><PERSON><PERSON>rapper, SyncClientWrapper
from ...core.request_options import RequestOptions
from ...types.dubbing_render_response_model import DubbingRenderResponseModel
from ...types.dubbing_resource import DubbingResource
from ...types.render_type import RenderType
from ...types.segment_dub_response import SegmentDubResponse
from ...types.segment_transcription_response import SegmentTranscriptionResponse
from ...types.segment_translation_response import SegmentTranslationResponse
from .language.client import AsyncLanguageClient, LanguageClient
from .raw_client import AsyncRawResourceClient, RawResourceClient
from .segment.client import AsyncSegmentClient, SegmentClient
from .speaker.client import AsyncSpeakerClient, SpeakerClient

# this is used as the default value for optional parameters
OMIT = typing.cast(typing.Any, ...)


class ResourceClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._raw_client = RawResourceClient(client_wrapper=client_wrapper)
        self.language = LanguageClient(client_wrapper=client_wrapper)

        self.segment = SegmentClient(client_wrapper=client_wrapper)

        self.speaker = SpeakerClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> RawResourceClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        RawResourceClient
        """
        return self._raw_client

    def get(self, dubbing_id: str, *, request_options: typing.Optional[RequestOptions] = None) -> DubbingResource:
        """
        Given a dubbing ID generated from the '/v1/dubbing' endpoint with studio enabled, returns the dubbing resource.

        Parameters
        ----------
        dubbing_id : str
            ID of the dubbing project.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        DubbingResource
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.dubbing.resource.get(
            dubbing_id="dubbing_id",
        )
        """
        _response = self._raw_client.get(dubbing_id, request_options=request_options)
        return _response.data

    def transcribe(
        self,
        dubbing_id: str,
        *,
        segments: typing.Sequence[str],
        request_options: typing.Optional[RequestOptions] = None,
    ) -> SegmentTranscriptionResponse:
        """
        Regenerate the transcriptions for the specified segments. Does not automatically regenerate translations or dubs.

        Parameters
        ----------
        dubbing_id : str
            ID of the dubbing project.

        segments : typing.Sequence[str]
            Transcribe this specific list of segments.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        SegmentTranscriptionResponse
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.dubbing.resource.transcribe(
            dubbing_id="dubbing_id",
            segments=["segments"],
        )
        """
        _response = self._raw_client.transcribe(dubbing_id, segments=segments, request_options=request_options)
        return _response.data

    def translate(
        self,
        dubbing_id: str,
        *,
        segments: typing.Sequence[str],
        languages: typing.Optional[typing.Sequence[str]] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> SegmentTranslationResponse:
        """
        Regenerate the translations for either the entire resource or the specified segments/languages. Will automatically transcribe missing transcriptions. Will not automatically regenerate the dubs.

        Parameters
        ----------
        dubbing_id : str
            ID of the dubbing project.

        segments : typing.Sequence[str]
            Translate only this list of segments.

        languages : typing.Optional[typing.Sequence[str]]
            Translate only these languages for each segment.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        SegmentTranslationResponse
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.dubbing.resource.translate(
            dubbing_id="dubbing_id",
            segments=["segments"],
        )
        """
        _response = self._raw_client.translate(
            dubbing_id, segments=segments, languages=languages, request_options=request_options
        )
        return _response.data

    def dub(
        self,
        dubbing_id: str,
        *,
        segments: typing.Sequence[str],
        languages: typing.Optional[typing.Sequence[str]] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> SegmentDubResponse:
        """
        Regenerate the dubs for either the entire resource or the specified segments/languages. Will automatically transcribe and translate any missing transcriptions and translations.

        Parameters
        ----------
        dubbing_id : str
            ID of the dubbing project.

        segments : typing.Sequence[str]
            Dub only this list of segments.

        languages : typing.Optional[typing.Sequence[str]]
            Dub only these languages for each segment.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        SegmentDubResponse
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.dubbing.resource.dub(
            dubbing_id="dubbing_id",
            segments=["segments"],
        )
        """
        _response = self._raw_client.dub(
            dubbing_id, segments=segments, languages=languages, request_options=request_options
        )
        return _response.data

    def render(
        self,
        dubbing_id: str,
        language: str,
        *,
        render_type: RenderType,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> DubbingRenderResponseModel:
        """
        Regenerate the output media for a language using the latest Studio state. Please ensure all segments have been dubbed before rendering, otherwise they will be omitted. Renders are generated asynchronously, and to check the status of all renders please use the 'Get Dubbing Resource' endpoint.

        Parameters
        ----------
        dubbing_id : str
            ID of the dubbing project.

        language : str
            Render this language

        render_type : RenderType
            The type of the render. One of ['mp4', 'aac', 'mp3', 'wav', 'aaf', 'tracks_zip', 'clips_zip']

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        DubbingRenderResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.dubbing.resource.render(
            dubbing_id="dubbing_id",
            language="language",
            render_type="mp4",
        )
        """
        _response = self._raw_client.render(
            dubbing_id, language, render_type=render_type, request_options=request_options
        )
        return _response.data


class AsyncResourceClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._raw_client = AsyncRawResourceClient(client_wrapper=client_wrapper)
        self.language = AsyncLanguageClient(client_wrapper=client_wrapper)

        self.segment = AsyncSegmentClient(client_wrapper=client_wrapper)

        self.speaker = AsyncSpeakerClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> AsyncRawResourceClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        AsyncRawResourceClient
        """
        return self._raw_client

    async def get(self, dubbing_id: str, *, request_options: typing.Optional[RequestOptions] = None) -> DubbingResource:
        """
        Given a dubbing ID generated from the '/v1/dubbing' endpoint with studio enabled, returns the dubbing resource.

        Parameters
        ----------
        dubbing_id : str
            ID of the dubbing project.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        DubbingResource
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.dubbing.resource.get(
                dubbing_id="dubbing_id",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.get(dubbing_id, request_options=request_options)
        return _response.data

    async def transcribe(
        self,
        dubbing_id: str,
        *,
        segments: typing.Sequence[str],
        request_options: typing.Optional[RequestOptions] = None,
    ) -> SegmentTranscriptionResponse:
        """
        Regenerate the transcriptions for the specified segments. Does not automatically regenerate translations or dubs.

        Parameters
        ----------
        dubbing_id : str
            ID of the dubbing project.

        segments : typing.Sequence[str]
            Transcribe this specific list of segments.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        SegmentTranscriptionResponse
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.dubbing.resource.transcribe(
                dubbing_id="dubbing_id",
                segments=["segments"],
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.transcribe(dubbing_id, segments=segments, request_options=request_options)
        return _response.data

    async def translate(
        self,
        dubbing_id: str,
        *,
        segments: typing.Sequence[str],
        languages: typing.Optional[typing.Sequence[str]] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> SegmentTranslationResponse:
        """
        Regenerate the translations for either the entire resource or the specified segments/languages. Will automatically transcribe missing transcriptions. Will not automatically regenerate the dubs.

        Parameters
        ----------
        dubbing_id : str
            ID of the dubbing project.

        segments : typing.Sequence[str]
            Translate only this list of segments.

        languages : typing.Optional[typing.Sequence[str]]
            Translate only these languages for each segment.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        SegmentTranslationResponse
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.dubbing.resource.translate(
                dubbing_id="dubbing_id",
                segments=["segments"],
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.translate(
            dubbing_id, segments=segments, languages=languages, request_options=request_options
        )
        return _response.data

    async def dub(
        self,
        dubbing_id: str,
        *,
        segments: typing.Sequence[str],
        languages: typing.Optional[typing.Sequence[str]] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> SegmentDubResponse:
        """
        Regenerate the dubs for either the entire resource or the specified segments/languages. Will automatically transcribe and translate any missing transcriptions and translations.

        Parameters
        ----------
        dubbing_id : str
            ID of the dubbing project.

        segments : typing.Sequence[str]
            Dub only this list of segments.

        languages : typing.Optional[typing.Sequence[str]]
            Dub only these languages for each segment.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        SegmentDubResponse
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.dubbing.resource.dub(
                dubbing_id="dubbing_id",
                segments=["segments"],
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.dub(
            dubbing_id, segments=segments, languages=languages, request_options=request_options
        )
        return _response.data

    async def render(
        self,
        dubbing_id: str,
        language: str,
        *,
        render_type: RenderType,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> DubbingRenderResponseModel:
        """
        Regenerate the output media for a language using the latest Studio state. Please ensure all segments have been dubbed before rendering, otherwise they will be omitted. Renders are generated asynchronously, and to check the status of all renders please use the 'Get Dubbing Resource' endpoint.

        Parameters
        ----------
        dubbing_id : str
            ID of the dubbing project.

        language : str
            Render this language

        render_type : RenderType
            The type of the render. One of ['mp4', 'aac', 'mp3', 'wav', 'aaf', 'tracks_zip', 'clips_zip']

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        DubbingRenderResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.dubbing.resource.render(
                dubbing_id="dubbing_id",
                language="language",
                render_type="mp4",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.render(
            dubbing_id, language, render_type=render_type, request_options=request_options
        )
        return _response.data

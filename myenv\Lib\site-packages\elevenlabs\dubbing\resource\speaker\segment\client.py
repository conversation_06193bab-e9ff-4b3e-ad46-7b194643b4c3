# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

from .....core.client_wrapper import AsyncC<PERSON><PERSON>rapper, SyncClientWrapper
from .....core.request_options import RequestOptions
from .....types.segment_create_response import SegmentCreateResponse
from .raw_client import AsyncRawSegmentClient, RawSegmentClient

# this is used as the default value for optional parameters
OMIT = typing.cast(typing.Any, ...)


class SegmentClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._raw_client = RawSegmentClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> RawSegmentClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        RawSegmentClient
        """
        return self._raw_client

    def create(
        self,
        dubbing_id: str,
        speaker_id: str,
        *,
        start_time: float,
        end_time: float,
        text: typing.Optional[str] = OMIT,
        translations: typing.Optional[typing.Dict[str, typing.Optional[str]]] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> SegmentCreateResponse:
        """
        Creates a new segment in dubbing resource with a start and end time for the speaker in every available language. Does not automatically generate transcripts/translations/audio.

        Parameters
        ----------
        dubbing_id : str
            ID of the dubbing project.

        speaker_id : str
            ID of the speaker.

        start_time : float

        end_time : float

        text : typing.Optional[str]

        translations : typing.Optional[typing.Dict[str, typing.Optional[str]]]

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        SegmentCreateResponse
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.dubbing.resource.speaker.segment.create(
            dubbing_id="dubbing_id",
            speaker_id="speaker_id",
            start_time=1.1,
            end_time=1.1,
        )
        """
        _response = self._raw_client.create(
            dubbing_id,
            speaker_id,
            start_time=start_time,
            end_time=end_time,
            text=text,
            translations=translations,
            request_options=request_options,
        )
        return _response.data


class AsyncSegmentClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._raw_client = AsyncRawSegmentClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> AsyncRawSegmentClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        AsyncRawSegmentClient
        """
        return self._raw_client

    async def create(
        self,
        dubbing_id: str,
        speaker_id: str,
        *,
        start_time: float,
        end_time: float,
        text: typing.Optional[str] = OMIT,
        translations: typing.Optional[typing.Dict[str, typing.Optional[str]]] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> SegmentCreateResponse:
        """
        Creates a new segment in dubbing resource with a start and end time for the speaker in every available language. Does not automatically generate transcripts/translations/audio.

        Parameters
        ----------
        dubbing_id : str
            ID of the dubbing project.

        speaker_id : str
            ID of the speaker.

        start_time : float

        end_time : float

        text : typing.Optional[str]

        translations : typing.Optional[typing.Dict[str, typing.Optional[str]]]

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        SegmentCreateResponse
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.dubbing.resource.speaker.segment.create(
                dubbing_id="dubbing_id",
                speaker_id="speaker_id",
                start_time=1.1,
                end_time=1.1,
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.create(
            dubbing_id,
            speaker_id,
            start_time=start_time,
            end_time=end_time,
            text=text,
            translations=translations,
            request_options=request_options,
        )
        return _response.data

# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing
from json.decoder import <PERSON><PERSON>NDecodeError

from ...core.api_error import ApiError
from ...core.client_wrapper import Async<PERSON>lient<PERSON>rapper, SyncClientWrapper
from ...core.http_response import Async<PERSON>ttp<PERSON><PERSON>ponse, HttpResponse
from ...core.request_options import RequestOptions
from ...core.unchecked_base_model import construct_type
from ...errors.unprocessable_entity_error import UnprocessableEntityError
from ...types.get_knowledge_base_list_response_model import GetKnowledgeBaseListResponseModel
from ...types.http_validation_error import HttpValidationError
from ...types.knowledge_base_document_type import KnowledgeBaseDocumentType


class RawKnowledgeBaseClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._client_wrapper = client_wrapper

    def list(
        self,
        *,
        cursor: typing.Optional[str] = None,
        page_size: typing.Optional[int] = None,
        search: typing.Optional[str] = None,
        show_only_owned_documents: typing.Optional[bool] = None,
        types: typing.Optional[
            typing.Union[KnowledgeBaseDocumentType, typing.Sequence[KnowledgeBaseDocumentType]]
        ] = None,
        use_typesense: typing.Optional[bool] = None,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> HttpResponse[GetKnowledgeBaseListResponseModel]:
        """
        Get a list of available knowledge base documents

        Parameters
        ----------
        cursor : typing.Optional[str]
            Used for fetching next page. Cursor is returned in the response.

        page_size : typing.Optional[int]
            How many documents to return at maximum. Can not exceed 100, defaults to 30.

        search : typing.Optional[str]
            If specified, the endpoint returns only such knowledge base documents whose names start with this string.

        show_only_owned_documents : typing.Optional[bool]
            If set to true, the endpoint will return only documents owned by you (and not shared from somebody else).

        types : typing.Optional[typing.Union[KnowledgeBaseDocumentType, typing.Sequence[KnowledgeBaseDocumentType]]]
            If present, the endpoint will return only documents of the given types.

        use_typesense : typing.Optional[bool]
            If set to true, the endpoint will use typesense DB to search for the documents).

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        HttpResponse[GetKnowledgeBaseListResponseModel]
            Successful Response
        """
        _response = self._client_wrapper.httpx_client.request(
            "v1/convai/knowledge-base",
            method="GET",
            params={
                "cursor": cursor,
                "page_size": page_size,
                "search": search,
                "show_only_owned_documents": show_only_owned_documents,
                "types": types,
                "use_typesense": use_typesense,
            },
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                _data = typing.cast(
                    GetKnowledgeBaseListResponseModel,
                    construct_type(
                        type_=GetKnowledgeBaseListResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
                return HttpResponse(response=_response, data=_data)
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    headers=dict(_response.headers),
                    body=typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response.text)
        raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)


class AsyncRawKnowledgeBaseClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._client_wrapper = client_wrapper

    async def list(
        self,
        *,
        cursor: typing.Optional[str] = None,
        page_size: typing.Optional[int] = None,
        search: typing.Optional[str] = None,
        show_only_owned_documents: typing.Optional[bool] = None,
        types: typing.Optional[
            typing.Union[KnowledgeBaseDocumentType, typing.Sequence[KnowledgeBaseDocumentType]]
        ] = None,
        use_typesense: typing.Optional[bool] = None,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> AsyncHttpResponse[GetKnowledgeBaseListResponseModel]:
        """
        Get a list of available knowledge base documents

        Parameters
        ----------
        cursor : typing.Optional[str]
            Used for fetching next page. Cursor is returned in the response.

        page_size : typing.Optional[int]
            How many documents to return at maximum. Can not exceed 100, defaults to 30.

        search : typing.Optional[str]
            If specified, the endpoint returns only such knowledge base documents whose names start with this string.

        show_only_owned_documents : typing.Optional[bool]
            If set to true, the endpoint will return only documents owned by you (and not shared from somebody else).

        types : typing.Optional[typing.Union[KnowledgeBaseDocumentType, typing.Sequence[KnowledgeBaseDocumentType]]]
            If present, the endpoint will return only documents of the given types.

        use_typesense : typing.Optional[bool]
            If set to true, the endpoint will use typesense DB to search for the documents).

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AsyncHttpResponse[GetKnowledgeBaseListResponseModel]
            Successful Response
        """
        _response = await self._client_wrapper.httpx_client.request(
            "v1/convai/knowledge-base",
            method="GET",
            params={
                "cursor": cursor,
                "page_size": page_size,
                "search": search,
                "show_only_owned_documents": show_only_owned_documents,
                "types": types,
                "use_typesense": use_typesense,
            },
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                _data = typing.cast(
                    GetKnowledgeBaseListResponseModel,
                    construct_type(
                        type_=GetKnowledgeBaseListResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
                return AsyncHttpResponse(response=_response, data=_data)
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    headers=dict(_response.headers),
                    body=typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response.text)
        raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)

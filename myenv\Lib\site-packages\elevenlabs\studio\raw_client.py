# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing
from json.decoder import JSONDecodeError

from ..core.api_error import ApiError
from ..core.client_wrapper import AsyncClientWrapper, SyncClientWrapper
from ..core.http_response import AsyncHttpResponse, HttpResponse
from ..core.request_options import RequestOptions
from ..core.serialization import convert_and_respect_annotation_metadata
from ..core.unchecked_base_model import construct_type
from ..errors.unprocessable_entity_error import UnprocessableEntityError
from ..types.http_validation_error import HttpValidationError
from ..types.podcast_project_response_model import PodcastProjectResponseModel
from .types.body_create_podcast_v_1_studio_podcasts_post_duration_scale import (
    BodyCreatePodcastV1StudioPodcastsPostDurationScale,
)
from .types.body_create_podcast_v_1_studio_podcasts_post_mode import BodyCreatePodcastV1StudioPodcastsPostMode
from .types.body_create_podcast_v_1_studio_podcasts_post_quality_preset import (
    BodyCreatePodcastV1StudioPodcastsPostQualityPreset,
)
from .types.body_create_podcast_v_1_studio_podcasts_post_source import BodyCreatePodcastV1StudioPodcastsPostSource

# this is used as the default value for optional parameters
OMIT = typing.cast(typing.Any, ...)


class RawStudioClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._client_wrapper = client_wrapper

    def create_podcast(
        self,
        *,
        model_id: str,
        mode: BodyCreatePodcastV1StudioPodcastsPostMode,
        source: BodyCreatePodcastV1StudioPodcastsPostSource,
        quality_preset: typing.Optional[BodyCreatePodcastV1StudioPodcastsPostQualityPreset] = OMIT,
        duration_scale: typing.Optional[BodyCreatePodcastV1StudioPodcastsPostDurationScale] = OMIT,
        language: typing.Optional[str] = OMIT,
        intro: typing.Optional[str] = OMIT,
        outro: typing.Optional[str] = OMIT,
        instructions_prompt: typing.Optional[str] = OMIT,
        highlights: typing.Optional[typing.Sequence[str]] = OMIT,
        callback_url: typing.Optional[str] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> HttpResponse[PodcastProjectResponseModel]:
        """
        Create and auto-convert a podcast project. Currently, the LLM cost is covered by us but you will still be charged for the audio generation. In the future, you will be charged for both the LLM and audio generation costs.

        Parameters
        ----------
        model_id : str
            The ID of the model to be used for this Studio project, you can query GET /v1/models to list all available models.

        mode : BodyCreatePodcastV1StudioPodcastsPostMode
            The type of podcast to generate. Can be 'conversation', an interaction between two voices, or 'bulletin', a monologue.

        source : BodyCreatePodcastV1StudioPodcastsPostSource
            The source content for the Podcast.

        quality_preset : typing.Optional[BodyCreatePodcastV1StudioPodcastsPostQualityPreset]
            Output quality of the generated audio. Must be one of:
            standard - standard output format, 128kbps with 44.1kHz sample rate.
            high - high quality output format, 192kbps with 44.1kHz sample rate and major improvements on our side. Using this setting increases the credit cost by 20%.
            ultra - ultra quality output format, 192kbps with 44.1kHz sample rate and highest improvements on our side. Using this setting increases the credit cost by 50%.
            ultra lossless - ultra quality output format, 705.6kbps with 44.1kHz sample rate and highest improvements on our side in a fully lossless format. Using this setting increases the credit cost by 100%.

        duration_scale : typing.Optional[BodyCreatePodcastV1StudioPodcastsPostDurationScale]
            Duration of the generated podcast. Must be one of:
            short - produces podcasts shorter than 3 minutes.
            default - produces podcasts roughly between 3-7 minutes.
            long - produces podcasts longer than 7 minutes.

        language : typing.Optional[str]
            An optional language of the Studio project. Two-letter language code (ISO 639-1).

        intro : typing.Optional[str]
            The intro text that will always be added to the beginning of the podcast.

        outro : typing.Optional[str]
            The outro text that will always be added to the end of the podcast.

        instructions_prompt : typing.Optional[str]
            Additional instructions prompt for the podcast generation used to adjust the podcast's style and tone.

        highlights : typing.Optional[typing.Sequence[str]]
            A brief summary or highlights of the Studio project's content, providing key points or themes. This should be between 10 and 70 characters.

        callback_url : typing.Optional[str]

                A url that will be called by our service when the Studio project is converted. Request will contain a json blob containing the status of the conversion
                Messages:
                1. When project was converted successfully:
                {
                  type: "project_conversion_status",
                  event_timestamp: 1234567890,
                  data: {
                    request_id: "1234567890",
                    project_id: "21m00Tcm4TlvDq8ikWAM",
                    conversion_status: "success",
                    project_snapshot_id: "22m00Tcm4TlvDq8ikMAT",
                    error_details: None,
                  }
                }
                2. When project conversion failed:
                {
                  type: "project_conversion_status",
                  event_timestamp: 1234567890,
                  data: {
                    request_id: "1234567890",
                    project_id: "21m00Tcm4TlvDq8ikWAM",
                    conversion_status: "error",
                    project_snapshot_id: None,
                    error_details: "Error details if conversion failed"
                  }
                }

                3. When chapter was converted successfully:
                {
                  type: "chapter_conversion_status",
                  event_timestamp: 1234567890,
                  data: {
                    request_id: "1234567890",
                    project_id: "21m00Tcm4TlvDq8ikWAM",
                    chapter_id: "22m00Tcm4TlvDq8ikMAT",
                    conversion_status: "success",
                    chapter_snapshot_id: "23m00Tcm4TlvDq8ikMAV",
                    error_details: None,
                  }
                }
                4. When chapter conversion failed:
                {
                  type: "chapter_conversion_status",
                  event_timestamp: 1234567890,
                  data: {
                    request_id: "1234567890",
                    project_id: "21m00Tcm4TlvDq8ikWAM",
                    chapter_id: "22m00Tcm4TlvDq8ikMAT",
                    conversion_status: "error",
                    chapter_snapshot_id: None,
                    error_details: "Error details if conversion failed"
                  }
                }


        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        HttpResponse[PodcastProjectResponseModel]
            Successful Response
        """
        _response = self._client_wrapper.httpx_client.request(
            "v1/studio/podcasts",
            method="POST",
            json={
                "model_id": model_id,
                "mode": convert_and_respect_annotation_metadata(
                    object_=mode, annotation=BodyCreatePodcastV1StudioPodcastsPostMode, direction="write"
                ),
                "source": convert_and_respect_annotation_metadata(
                    object_=source, annotation=BodyCreatePodcastV1StudioPodcastsPostSource, direction="write"
                ),
                "quality_preset": quality_preset,
                "duration_scale": duration_scale,
                "language": language,
                "intro": intro,
                "outro": outro,
                "instructions_prompt": instructions_prompt,
                "highlights": highlights,
                "callback_url": callback_url,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                _data = typing.cast(
                    PodcastProjectResponseModel,
                    construct_type(
                        type_=PodcastProjectResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
                return HttpResponse(response=_response, data=_data)
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    headers=dict(_response.headers),
                    body=typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response.text)
        raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)


class AsyncRawStudioClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._client_wrapper = client_wrapper

    async def create_podcast(
        self,
        *,
        model_id: str,
        mode: BodyCreatePodcastV1StudioPodcastsPostMode,
        source: BodyCreatePodcastV1StudioPodcastsPostSource,
        quality_preset: typing.Optional[BodyCreatePodcastV1StudioPodcastsPostQualityPreset] = OMIT,
        duration_scale: typing.Optional[BodyCreatePodcastV1StudioPodcastsPostDurationScale] = OMIT,
        language: typing.Optional[str] = OMIT,
        intro: typing.Optional[str] = OMIT,
        outro: typing.Optional[str] = OMIT,
        instructions_prompt: typing.Optional[str] = OMIT,
        highlights: typing.Optional[typing.Sequence[str]] = OMIT,
        callback_url: typing.Optional[str] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> AsyncHttpResponse[PodcastProjectResponseModel]:
        """
        Create and auto-convert a podcast project. Currently, the LLM cost is covered by us but you will still be charged for the audio generation. In the future, you will be charged for both the LLM and audio generation costs.

        Parameters
        ----------
        model_id : str
            The ID of the model to be used for this Studio project, you can query GET /v1/models to list all available models.

        mode : BodyCreatePodcastV1StudioPodcastsPostMode
            The type of podcast to generate. Can be 'conversation', an interaction between two voices, or 'bulletin', a monologue.

        source : BodyCreatePodcastV1StudioPodcastsPostSource
            The source content for the Podcast.

        quality_preset : typing.Optional[BodyCreatePodcastV1StudioPodcastsPostQualityPreset]
            Output quality of the generated audio. Must be one of:
            standard - standard output format, 128kbps with 44.1kHz sample rate.
            high - high quality output format, 192kbps with 44.1kHz sample rate and major improvements on our side. Using this setting increases the credit cost by 20%.
            ultra - ultra quality output format, 192kbps with 44.1kHz sample rate and highest improvements on our side. Using this setting increases the credit cost by 50%.
            ultra lossless - ultra quality output format, 705.6kbps with 44.1kHz sample rate and highest improvements on our side in a fully lossless format. Using this setting increases the credit cost by 100%.

        duration_scale : typing.Optional[BodyCreatePodcastV1StudioPodcastsPostDurationScale]
            Duration of the generated podcast. Must be one of:
            short - produces podcasts shorter than 3 minutes.
            default - produces podcasts roughly between 3-7 minutes.
            long - produces podcasts longer than 7 minutes.

        language : typing.Optional[str]
            An optional language of the Studio project. Two-letter language code (ISO 639-1).

        intro : typing.Optional[str]
            The intro text that will always be added to the beginning of the podcast.

        outro : typing.Optional[str]
            The outro text that will always be added to the end of the podcast.

        instructions_prompt : typing.Optional[str]
            Additional instructions prompt for the podcast generation used to adjust the podcast's style and tone.

        highlights : typing.Optional[typing.Sequence[str]]
            A brief summary or highlights of the Studio project's content, providing key points or themes. This should be between 10 and 70 characters.

        callback_url : typing.Optional[str]

                A url that will be called by our service when the Studio project is converted. Request will contain a json blob containing the status of the conversion
                Messages:
                1. When project was converted successfully:
                {
                  type: "project_conversion_status",
                  event_timestamp: 1234567890,
                  data: {
                    request_id: "1234567890",
                    project_id: "21m00Tcm4TlvDq8ikWAM",
                    conversion_status: "success",
                    project_snapshot_id: "22m00Tcm4TlvDq8ikMAT",
                    error_details: None,
                  }
                }
                2. When project conversion failed:
                {
                  type: "project_conversion_status",
                  event_timestamp: 1234567890,
                  data: {
                    request_id: "1234567890",
                    project_id: "21m00Tcm4TlvDq8ikWAM",
                    conversion_status: "error",
                    project_snapshot_id: None,
                    error_details: "Error details if conversion failed"
                  }
                }

                3. When chapter was converted successfully:
                {
                  type: "chapter_conversion_status",
                  event_timestamp: 1234567890,
                  data: {
                    request_id: "1234567890",
                    project_id: "21m00Tcm4TlvDq8ikWAM",
                    chapter_id: "22m00Tcm4TlvDq8ikMAT",
                    conversion_status: "success",
                    chapter_snapshot_id: "23m00Tcm4TlvDq8ikMAV",
                    error_details: None,
                  }
                }
                4. When chapter conversion failed:
                {
                  type: "chapter_conversion_status",
                  event_timestamp: 1234567890,
                  data: {
                    request_id: "1234567890",
                    project_id: "21m00Tcm4TlvDq8ikWAM",
                    chapter_id: "22m00Tcm4TlvDq8ikMAT",
                    conversion_status: "error",
                    chapter_snapshot_id: None,
                    error_details: "Error details if conversion failed"
                  }
                }


        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AsyncHttpResponse[PodcastProjectResponseModel]
            Successful Response
        """
        _response = await self._client_wrapper.httpx_client.request(
            "v1/studio/podcasts",
            method="POST",
            json={
                "model_id": model_id,
                "mode": convert_and_respect_annotation_metadata(
                    object_=mode, annotation=BodyCreatePodcastV1StudioPodcastsPostMode, direction="write"
                ),
                "source": convert_and_respect_annotation_metadata(
                    object_=source, annotation=BodyCreatePodcastV1StudioPodcastsPostSource, direction="write"
                ),
                "quality_preset": quality_preset,
                "duration_scale": duration_scale,
                "language": language,
                "intro": intro,
                "outro": outro,
                "instructions_prompt": instructions_prompt,
                "highlights": highlights,
                "callback_url": callback_url,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                _data = typing.cast(
                    PodcastProjectResponseModel,
                    construct_type(
                        type_=PodcastProjectResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
                return AsyncHttpResponse(response=_response, data=_data)
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    headers=dict(_response.headers),
                    body=typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response.text)
        raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)

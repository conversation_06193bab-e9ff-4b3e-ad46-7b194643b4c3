# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

from ...core.client_wrapper import AsyncClient<PERSON>rapper, SyncClientWrapper
from ...core.request_options import RequestOptions
from ...types.conversation_signed_url_response_model import ConversationSignedUrlResponseModel
from ...types.evaluation_success_result import Evaluation<PERSON>uccessR<PERSON>ult
from ...types.get_conversation_response_model import GetConversationResponseModel
from ...types.get_conversations_page_response_model import GetConversationsPageResponseModel
from .audio.client import AsyncAudioClient, AudioClient
from .feedback.client import AsyncFeedbackClient, FeedbackClient
from .raw_client import AsyncRawConversationsClient, RawConversationsClient


class ConversationsClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._raw_client = RawConversationsClient(client_wrapper=client_wrapper)
        self.audio = AudioClient(client_wrapper=client_wrapper)

        self.feedback = FeedbackClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> RawConversationsClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        RawConversationsClient
        """
        return self._raw_client

    def get_signed_url(
        self, *, agent_id: str, request_options: typing.Optional[RequestOptions] = None
    ) -> ConversationSignedUrlResponseModel:
        """
        Get a signed url to start a conversation with an agent with an agent that requires authorization

        Parameters
        ----------
        agent_id : str
            The id of the agent you're taking the action on.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        ConversationSignedUrlResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.conversational_ai.conversations.get_signed_url(
            agent_id="21m00Tcm4TlvDq8ikWAM",
        )
        """
        _response = self._raw_client.get_signed_url(agent_id=agent_id, request_options=request_options)
        return _response.data

    def list(
        self,
        *,
        cursor: typing.Optional[str] = None,
        agent_id: typing.Optional[str] = None,
        call_successful: typing.Optional[EvaluationSuccessResult] = None,
        call_start_before_unix: typing.Optional[int] = None,
        call_start_after_unix: typing.Optional[int] = None,
        page_size: typing.Optional[int] = None,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> GetConversationsPageResponseModel:
        """
        Get all conversations of agents that user owns. With option to restrict to a specific agent.

        Parameters
        ----------
        cursor : typing.Optional[str]
            Used for fetching next page. Cursor is returned in the response.

        agent_id : typing.Optional[str]
            The id of the agent you're taking the action on.

        call_successful : typing.Optional[EvaluationSuccessResult]
            The result of the success evaluation

        call_start_before_unix : typing.Optional[int]
            Unix timestamp (in seconds) to filter conversations up to this start date.

        call_start_after_unix : typing.Optional[int]
            Unix timestamp (in seconds) to filter conversations after to this start date.

        page_size : typing.Optional[int]
            How many conversations to return at maximum. Can not exceed 100, defaults to 30.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        GetConversationsPageResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.conversational_ai.conversations.list()
        """
        _response = self._raw_client.list(
            cursor=cursor,
            agent_id=agent_id,
            call_successful=call_successful,
            call_start_before_unix=call_start_before_unix,
            call_start_after_unix=call_start_after_unix,
            page_size=page_size,
            request_options=request_options,
        )
        return _response.data

    def get(
        self, conversation_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> GetConversationResponseModel:
        """
        Get the details of a particular conversation

        Parameters
        ----------
        conversation_id : str
            The id of the conversation you're taking the action on.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        GetConversationResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.conversational_ai.conversations.get(
            conversation_id="123",
        )
        """
        _response = self._raw_client.get(conversation_id, request_options=request_options)
        return _response.data

    def delete(
        self, conversation_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> typing.Optional[typing.Any]:
        """
        Delete a particular conversation

        Parameters
        ----------
        conversation_id : str
            The id of the conversation you're taking the action on.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.Optional[typing.Any]
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.conversational_ai.conversations.delete(
            conversation_id="21m00Tcm4TlvDq8ikWAM",
        )
        """
        _response = self._raw_client.delete(conversation_id, request_options=request_options)
        return _response.data


class AsyncConversationsClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._raw_client = AsyncRawConversationsClient(client_wrapper=client_wrapper)
        self.audio = AsyncAudioClient(client_wrapper=client_wrapper)

        self.feedback = AsyncFeedbackClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> AsyncRawConversationsClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        AsyncRawConversationsClient
        """
        return self._raw_client

    async def get_signed_url(
        self, *, agent_id: str, request_options: typing.Optional[RequestOptions] = None
    ) -> ConversationSignedUrlResponseModel:
        """
        Get a signed url to start a conversation with an agent with an agent that requires authorization

        Parameters
        ----------
        agent_id : str
            The id of the agent you're taking the action on.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        ConversationSignedUrlResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.conversational_ai.conversations.get_signed_url(
                agent_id="21m00Tcm4TlvDq8ikWAM",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.get_signed_url(agent_id=agent_id, request_options=request_options)
        return _response.data

    async def list(
        self,
        *,
        cursor: typing.Optional[str] = None,
        agent_id: typing.Optional[str] = None,
        call_successful: typing.Optional[EvaluationSuccessResult] = None,
        call_start_before_unix: typing.Optional[int] = None,
        call_start_after_unix: typing.Optional[int] = None,
        page_size: typing.Optional[int] = None,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> GetConversationsPageResponseModel:
        """
        Get all conversations of agents that user owns. With option to restrict to a specific agent.

        Parameters
        ----------
        cursor : typing.Optional[str]
            Used for fetching next page. Cursor is returned in the response.

        agent_id : typing.Optional[str]
            The id of the agent you're taking the action on.

        call_successful : typing.Optional[EvaluationSuccessResult]
            The result of the success evaluation

        call_start_before_unix : typing.Optional[int]
            Unix timestamp (in seconds) to filter conversations up to this start date.

        call_start_after_unix : typing.Optional[int]
            Unix timestamp (in seconds) to filter conversations after to this start date.

        page_size : typing.Optional[int]
            How many conversations to return at maximum. Can not exceed 100, defaults to 30.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        GetConversationsPageResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.conversational_ai.conversations.list()


        asyncio.run(main())
        """
        _response = await self._raw_client.list(
            cursor=cursor,
            agent_id=agent_id,
            call_successful=call_successful,
            call_start_before_unix=call_start_before_unix,
            call_start_after_unix=call_start_after_unix,
            page_size=page_size,
            request_options=request_options,
        )
        return _response.data

    async def get(
        self, conversation_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> GetConversationResponseModel:
        """
        Get the details of a particular conversation

        Parameters
        ----------
        conversation_id : str
            The id of the conversation you're taking the action on.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        GetConversationResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.conversational_ai.conversations.get(
                conversation_id="123",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.get(conversation_id, request_options=request_options)
        return _response.data

    async def delete(
        self, conversation_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> typing.Optional[typing.Any]:
        """
        Delete a particular conversation

        Parameters
        ----------
        conversation_id : str
            The id of the conversation you're taking the action on.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.Optional[typing.Any]
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.conversational_ai.conversations.delete(
                conversation_id="21m00Tcm4TlvDq8ikWAM",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.delete(conversation_id, request_options=request_options)
        return _response.data

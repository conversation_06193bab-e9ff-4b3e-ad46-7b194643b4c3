# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

from ....core.client_wrapper import Async<PERSON><PERSON><PERSON>rapper, SyncClientWrapper
from ....core.request_options import RequestOptions
from ....types.user_feedback_score import UserFeedbackScore
from .raw_client import Async<PERSON><PERSON><PERSON><PERSON>back<PERSON>lient, RawFeedbackClient

# this is used as the default value for optional parameters
OMIT = typing.cast(typing.Any, ...)


class FeedbackClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._raw_client = RawFeedbackClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> RawFeedbackClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        RawFeedbackClient
        """
        return self._raw_client

    def create(
        self,
        conversation_id: str,
        *,
        feedback: UserFeedbackScore,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> typing.Optional[typing.Any]:
        """
        Send the feedback for the given conversation

        Parameters
        ----------
        conversation_id : str
            The id of the conversation you're taking the action on.

        feedback : UserFeedbackScore
            Either 'like' or 'dislike' to indicate the feedback for the conversation.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.Optional[typing.Any]
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.conversational_ai.conversations.feedback.create(
            conversation_id="21m00Tcm4TlvDq8ikWAM",
            feedback="like",
        )
        """
        _response = self._raw_client.create(conversation_id, feedback=feedback, request_options=request_options)
        return _response.data


class AsyncFeedbackClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._raw_client = AsyncRawFeedbackClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> AsyncRawFeedbackClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        AsyncRawFeedbackClient
        """
        return self._raw_client

    async def create(
        self,
        conversation_id: str,
        *,
        feedback: UserFeedbackScore,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> typing.Optional[typing.Any]:
        """
        Send the feedback for the given conversation

        Parameters
        ----------
        conversation_id : str
            The id of the conversation you're taking the action on.

        feedback : UserFeedbackScore
            Either 'like' or 'dislike' to indicate the feedback for the conversation.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.Optional[typing.Any]
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.conversational_ai.conversations.feedback.create(
                conversation_id="21m00Tcm4TlvDq8ikWAM",
                feedback="like",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.create(conversation_id, feedback=feedback, request_options=request_options)
        return _response.data

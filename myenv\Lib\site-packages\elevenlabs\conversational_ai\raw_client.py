# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing
from json.decoder import <PERSON><PERSON><PERSON>ecodeError

from .. import core
from ..core.api_error import ApiError
from ..core.client_wrapper import Async<PERSON>lient<PERSON>rapper, SyncClientWrapper
from ..core.http_response import Async<PERSON>ttpR<PERSON>ponse, HttpResponse
from ..core.jsonable_encoder import jsonable_encoder
from ..core.request_options import RequestOptions
from ..core.unchecked_base_model import construct_type
from ..errors.unprocessable_entity_error import UnprocessableEntityError
from ..types.add_knowledge_base_response_model import AddKnowledgeBaseResponseModel
from ..types.http_validation_error import HttpValidationError
from ..types.post_workspace_secret_response_model import PostWorkspaceSecretResponseModel
from ..types.rag_document_index_response_model import RagDocumentIndexResponseModel
from ..types.rag_document_indexes_response_model import RagDocumentIndexesResponseModel
from ..types.rag_index_overview_response_model import RagIndexOverviewResponseModel

# this is used as the default value for optional parameters
OMIT = typing.cast(typing.Any, ...)


class RawConversationalAiClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._client_wrapper = client_wrapper

    def add_to_knowledge_base(
        self,
        *,
        agent_id: typing.Optional[str] = None,
        name: typing.Optional[str] = OMIT,
        url: typing.Optional[str] = OMIT,
        file: typing.Optional[core.File] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> HttpResponse[AddKnowledgeBaseResponseModel]:
        """
        Upload a file or webpage URL to create a knowledge base document. <br> <Note> After creating the document, update the agent's knowledge base by calling [Update agent](/docs/conversational-ai/api-reference/agents/update-agent). </Note>

        Parameters
        ----------
        agent_id : typing.Optional[str]

        name : typing.Optional[str]
            A custom, human-readable name for the document.

        url : typing.Optional[str]
            URL to a page of documentation that the agent will have access to in order to interact with users.

        file : typing.Optional[core.File]
            See core.File for more documentation

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        HttpResponse[AddKnowledgeBaseResponseModel]
            Successful Response
        """
        _response = self._client_wrapper.httpx_client.request(
            "v1/convai/knowledge-base",
            method="POST",
            params={
                "agent_id": agent_id,
            },
            data={
                "name": name,
                "url": url,
            },
            files={
                **({"file": file} if file is not None else {}),
            },
            request_options=request_options,
            omit=OMIT,
            force_multipart=True,
        )
        try:
            if 200 <= _response.status_code < 300:
                _data = typing.cast(
                    AddKnowledgeBaseResponseModel,
                    construct_type(
                        type_=AddKnowledgeBaseResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
                return HttpResponse(response=_response, data=_data)
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    headers=dict(_response.headers),
                    body=typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response.text)
        raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)

    def get_document_rag_indexes(
        self, documentation_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> HttpResponse[RagDocumentIndexesResponseModel]:
        """
        Provides information about all RAG indexes of the specified knowledgebase document.

        Parameters
        ----------
        documentation_id : str
            The id of a document from the knowledge base. This is returned on document addition.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        HttpResponse[RagDocumentIndexesResponseModel]
            Successful Response
        """
        _response = self._client_wrapper.httpx_client.request(
            f"v1/convai/knowledge-base/{jsonable_encoder(documentation_id)}/rag-index",
            method="GET",
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                _data = typing.cast(
                    RagDocumentIndexesResponseModel,
                    construct_type(
                        type_=RagDocumentIndexesResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
                return HttpResponse(response=_response, data=_data)
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    headers=dict(_response.headers),
                    body=typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response.text)
        raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)

    def delete_document_rag_index(
        self, documentation_id: str, rag_index_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> HttpResponse[RagDocumentIndexResponseModel]:
        """
        Delete RAG index for the knowledgebase document.

        Parameters
        ----------
        documentation_id : str
            The id of a document from the knowledge base. This is returned on document addition.

        rag_index_id : str
            The id of RAG index of document from the knowledge base.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        HttpResponse[RagDocumentIndexResponseModel]
            Successful Response
        """
        _response = self._client_wrapper.httpx_client.request(
            f"v1/convai/knowledge-base/{jsonable_encoder(documentation_id)}/rag-index/{jsonable_encoder(rag_index_id)}",
            method="DELETE",
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                _data = typing.cast(
                    RagDocumentIndexResponseModel,
                    construct_type(
                        type_=RagDocumentIndexResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
                return HttpResponse(response=_response, data=_data)
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    headers=dict(_response.headers),
                    body=typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response.text)
        raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)

    def rag_index_overview(
        self, *, request_options: typing.Optional[RequestOptions] = None
    ) -> HttpResponse[RagIndexOverviewResponseModel]:
        """
        Provides total size and other information of RAG indexes used by knowledgebase documents

        Parameters
        ----------
        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        HttpResponse[RagIndexOverviewResponseModel]
            Successful Response
        """
        _response = self._client_wrapper.httpx_client.request(
            "v1/convai/knowledge-base/rag-index",
            method="GET",
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                _data = typing.cast(
                    RagIndexOverviewResponseModel,
                    construct_type(
                        type_=RagIndexOverviewResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
                return HttpResponse(response=_response, data=_data)
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    headers=dict(_response.headers),
                    body=typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response.text)
        raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)

    def update_secret(
        self, secret_id: str, *, name: str, value: str, request_options: typing.Optional[RequestOptions] = None
    ) -> HttpResponse[PostWorkspaceSecretResponseModel]:
        """
        Update an existing secret for the workspace

        Parameters
        ----------
        secret_id : str

        name : str

        value : str

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        HttpResponse[PostWorkspaceSecretResponseModel]
            Successful Response
        """
        _response = self._client_wrapper.httpx_client.request(
            f"v1/convai/secrets/{jsonable_encoder(secret_id)}",
            method="PATCH",
            json={
                "name": name,
                "value": value,
                "type": "update",
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                _data = typing.cast(
                    PostWorkspaceSecretResponseModel,
                    construct_type(
                        type_=PostWorkspaceSecretResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
                return HttpResponse(response=_response, data=_data)
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    headers=dict(_response.headers),
                    body=typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response.text)
        raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)


class AsyncRawConversationalAiClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._client_wrapper = client_wrapper

    async def add_to_knowledge_base(
        self,
        *,
        agent_id: typing.Optional[str] = None,
        name: typing.Optional[str] = OMIT,
        url: typing.Optional[str] = OMIT,
        file: typing.Optional[core.File] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> AsyncHttpResponse[AddKnowledgeBaseResponseModel]:
        """
        Upload a file or webpage URL to create a knowledge base document. <br> <Note> After creating the document, update the agent's knowledge base by calling [Update agent](/docs/conversational-ai/api-reference/agents/update-agent). </Note>

        Parameters
        ----------
        agent_id : typing.Optional[str]

        name : typing.Optional[str]
            A custom, human-readable name for the document.

        url : typing.Optional[str]
            URL to a page of documentation that the agent will have access to in order to interact with users.

        file : typing.Optional[core.File]
            See core.File for more documentation

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AsyncHttpResponse[AddKnowledgeBaseResponseModel]
            Successful Response
        """
        _response = await self._client_wrapper.httpx_client.request(
            "v1/convai/knowledge-base",
            method="POST",
            params={
                "agent_id": agent_id,
            },
            data={
                "name": name,
                "url": url,
            },
            files={
                **({"file": file} if file is not None else {}),
            },
            request_options=request_options,
            omit=OMIT,
            force_multipart=True,
        )
        try:
            if 200 <= _response.status_code < 300:
                _data = typing.cast(
                    AddKnowledgeBaseResponseModel,
                    construct_type(
                        type_=AddKnowledgeBaseResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
                return AsyncHttpResponse(response=_response, data=_data)
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    headers=dict(_response.headers),
                    body=typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response.text)
        raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)

    async def get_document_rag_indexes(
        self, documentation_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> AsyncHttpResponse[RagDocumentIndexesResponseModel]:
        """
        Provides information about all RAG indexes of the specified knowledgebase document.

        Parameters
        ----------
        documentation_id : str
            The id of a document from the knowledge base. This is returned on document addition.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AsyncHttpResponse[RagDocumentIndexesResponseModel]
            Successful Response
        """
        _response = await self._client_wrapper.httpx_client.request(
            f"v1/convai/knowledge-base/{jsonable_encoder(documentation_id)}/rag-index",
            method="GET",
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                _data = typing.cast(
                    RagDocumentIndexesResponseModel,
                    construct_type(
                        type_=RagDocumentIndexesResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
                return AsyncHttpResponse(response=_response, data=_data)
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    headers=dict(_response.headers),
                    body=typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response.text)
        raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)

    async def delete_document_rag_index(
        self, documentation_id: str, rag_index_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> AsyncHttpResponse[RagDocumentIndexResponseModel]:
        """
        Delete RAG index for the knowledgebase document.

        Parameters
        ----------
        documentation_id : str
            The id of a document from the knowledge base. This is returned on document addition.

        rag_index_id : str
            The id of RAG index of document from the knowledge base.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AsyncHttpResponse[RagDocumentIndexResponseModel]
            Successful Response
        """
        _response = await self._client_wrapper.httpx_client.request(
            f"v1/convai/knowledge-base/{jsonable_encoder(documentation_id)}/rag-index/{jsonable_encoder(rag_index_id)}",
            method="DELETE",
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                _data = typing.cast(
                    RagDocumentIndexResponseModel,
                    construct_type(
                        type_=RagDocumentIndexResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
                return AsyncHttpResponse(response=_response, data=_data)
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    headers=dict(_response.headers),
                    body=typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response.text)
        raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)

    async def rag_index_overview(
        self, *, request_options: typing.Optional[RequestOptions] = None
    ) -> AsyncHttpResponse[RagIndexOverviewResponseModel]:
        """
        Provides total size and other information of RAG indexes used by knowledgebase documents

        Parameters
        ----------
        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AsyncHttpResponse[RagIndexOverviewResponseModel]
            Successful Response
        """
        _response = await self._client_wrapper.httpx_client.request(
            "v1/convai/knowledge-base/rag-index",
            method="GET",
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                _data = typing.cast(
                    RagIndexOverviewResponseModel,
                    construct_type(
                        type_=RagIndexOverviewResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
                return AsyncHttpResponse(response=_response, data=_data)
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    headers=dict(_response.headers),
                    body=typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response.text)
        raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)

    async def update_secret(
        self, secret_id: str, *, name: str, value: str, request_options: typing.Optional[RequestOptions] = None
    ) -> AsyncHttpResponse[PostWorkspaceSecretResponseModel]:
        """
        Update an existing secret for the workspace

        Parameters
        ----------
        secret_id : str

        name : str

        value : str

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AsyncHttpResponse[PostWorkspaceSecretResponseModel]
            Successful Response
        """
        _response = await self._client_wrapper.httpx_client.request(
            f"v1/convai/secrets/{jsonable_encoder(secret_id)}",
            method="PATCH",
            json={
                "name": name,
                "value": value,
                "type": "update",
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                _data = typing.cast(
                    PostWorkspaceSecretResponseModel,
                    construct_type(
                        type_=PostWorkspaceSecretResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
                return AsyncHttpResponse(response=_response, data=_data)
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    headers=dict(_response.headers),
                    body=typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response.text)
        raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)

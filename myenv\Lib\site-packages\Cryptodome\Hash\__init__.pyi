from typing import overload
from typing_extensions import Literal

from Cryptodome.Hash.SHA1 import SHA1Hash
from Cryptodome.Hash.SHA224 import SHA224Hash
from Cryptodome.Hash.SHA256 import SHA256Hash
from Cryptodome.Hash.SHA384 import SHA384Hash
from Cryptodome.Hash.SHA512 import SHA512Hash
from Cryptodome.Hash.SHA3_224 import SHA3_224_Hash
from Cryptodome.Hash.SHA3_256 import SHA3_256_Hash
from Cryptodome.Hash.SHA3_384 import SHA3_384_Hash
from Cryptodome.Hash.SHA3_512 import SHA3_512_Hash

@overload
def new(name: Literal["1.3.14.3.2.26"]) -> SHA1Hash: ...
@overload
def new(name: Literal["SHA1"]) -> SHA1Hash: ...
@overload
def new(name: Literal["2.16.840.1.101.3.4.2.4"]) -> SHA224Hash: ...
@overload
def new(name: Literal["SHA224"]) -> SHA224Hash: ...
@overload
def new(name: Literal["2.16.840.1.101.3.4.2.1"]) -> SHA256Hash: ...
@overload
def new(name: Literal["SHA256"]) -> SHA256Hash: ...
@overload
def new(name: Literal["2.16.840.1.101.3.4.2.2"]) -> SHA384Hash: ...
@overload
def new(name: Literal["SHA384"]) -> SHA384Hash: ...
@overload
def new(name: Literal["2.16.840.1.101.3.4.2.3"]) -> SHA512Hash: ...
@overload
def new(name: Literal["SHA512"]) -> SHA512Hash: ...
@overload
def new(name: Literal["2.16.840.1.101.3.4.2.5"]) -> SHA512Hash: ...
@overload
def new(name: Literal["SHA512-224"]) -> SHA512Hash: ...
@overload
def new(name: Literal["2.16.840.1.101.3.4.2.6"]) -> SHA512Hash: ...
@overload
def new(name: Literal["SHA512-256"]) -> SHA512Hash: ...
@overload
def new(name: Literal["2.16.840.1.101.3.4.2.7"]) -> SHA3_224_Hash: ...
@overload
def new(name: Literal["SHA3-224"]) -> SHA3_224_Hash: ...
@overload
def new(name: Literal["2.16.840.1.101.3.4.2.8"]) -> SHA3_256_Hash: ...
@overload
def new(name: Literal["SHA3-256"]) -> SHA3_256_Hash: ...
@overload
def new(name: Literal["2.16.840.1.101.3.4.2.9"]) -> SHA3_384_Hash: ...
@overload
def new(name: Literal["SHA3-384"]) -> SHA3_384_Hash: ...
@overload
def new(name: Literal["2.16.840.1.101.3.4.2.10"]) -> SHA3_512_Hash: ...
@overload
def new(name: Literal["SHA3-512"]) -> SHA3_512_Hash: ...
